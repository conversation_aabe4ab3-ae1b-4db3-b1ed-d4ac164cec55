<execution>
  <constraint>
    ## 技术约束
    - **构建时间**：单平台构建时间 < 10分钟，全平台 < 30分钟
    - **包体积**：安装包大小 < 200MB，解压后 < 500MB
    - **构建成功率**：CI/CD构建成功率 > 95%
    - **部署频率**：支持每日多次部署
    - **回滚时间**：问题版本回滚时间 < 5分钟
    - **平台支持**：Windows 10+、macOS 10.15+、Ubuntu 18.04+
  </constraint>

  <rule>
    ## 运维规则
    - **版本控制**：所有发布版本必须有对应的Git标签
    - **测试先行**：所有代码必须通过自动化测试
    - **安全检查**：必须进行依赖漏洞扫描和代码安全检查
    - **签名认证**：发布版本必须进行代码签名
    - **文档同步**：构建配置变更必须更新文档
    - **监控告警**：关键指标异常必须及时告警
  </rule>

  <guideline>
    ## 运维指导原则
    - **自动化优先**：能自动化的流程绝不手动操作
    - **失败快速**：尽早发现问题，快速失败
    - **可重复性**：确保构建结果的一致性和可重复性
    - **可观测性**：完善的日志记录和监控指标
    - **安全第一**：所有流程都要考虑安全性
    - **持续改进**：定期优化构建流程和工具
  </guideline>

  <process>
    ## DevOps实施流程
    
    ### Step 1: 构建配置
    ```json
    // package.json - 构建脚本配置
    {
      "name": "ai-novel-assistant",
      "version": "1.0.0",
      "main": "dist-main/index.js",
      "scripts": {
        "dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"",
        "dev:main": "tsc -w -p src/main",
        "dev:renderer": "vite",
        "build": "npm run build:main && npm run build:renderer",
        "build:main": "tsc -p src/main",
        "build:renderer": "vite build",
        "build:all": "npm run build && electron-builder --publish=never",
        "build:win": "npm run build && electron-builder --win --publish=never",
        "build:mac": "npm run build && electron-builder --mac --publish=never",
        "build:linux": "npm run build && electron-builder --linux --publish=never",
        "dist": "npm run build && electron-builder --publish=always",
        "test": "vitest",
        "test:e2e": "playwright test",
        "lint": "eslint src --ext .ts,.vue",
        "type-check": "vue-tsc --noEmit"
      },
      "build": {
        "appId": "com.ainovel.assistant",
        "productName": "AI小说助手",
        "directories": {
          "output": "dist",
          "buildResources": "build"
        },
        "files": [
          "dist-main/**/*",
          "dist-renderer/**/*",
          "node_modules/**/*",
          "!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}",
          "!node_modules/*/{test,__tests__,tests,powered-test,example,examples}",
          "!node_modules/*.d.ts",
          "!node_modules/.bin",
          "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}",
          "!.editorconfig",
          "!**/._*",
          "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}",
          "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}",
          "!**/{appveyor.yml,.travis.yml,circle.yml}",
          "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"
        ],
        "win": {
          "target": [
            {
              "target": "nsis",
              "arch": ["x64"]
            },
            {
              "target": "portable",
              "arch": ["x64"]
            }
          ],
          "icon": "build/icon.ico",
          "requestedExecutionLevel": "asInvoker"
        },
        "mac": {
          "target": [
            {
              "target": "dmg",
              "arch": ["x64", "arm64"]
            }
          ],
          "icon": "build/icon.icns",
          "category": "public.app-category.productivity"
        },
        "linux": {
          "target": [
            {
              "target": "AppImage",
              "arch": ["x64"]
            },
            {
              "target": "deb",
              "arch": ["x64"]
            }
          ],
          "icon": "build/icon.png",
          "category": "Office"
        },
        "nsis": {
          "oneClick": false,
          "allowToChangeInstallationDirectory": true,
          "createDesktopShortcut": true,
          "createStartMenuShortcut": true
        },
        "publish": {
          "provider": "github",
          "owner": "your-username",
          "repo": "ai-novel-assistant"
        }
      }
    }
    ```
    
    ### Step 2: GitHub Actions CI/CD
    ```yaml
    # .github/workflows/build.yml
    name: Build and Release
    
    on:
      push:
        branches: [ main, develop ]
        tags: [ 'v*' ]
      pull_request:
        branches: [ main ]
    
    jobs:
      test:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/checkout@v4
          
          - name: Setup Node.js
            uses: actions/setup-node@v4
            with:
              node-version: '20'
              cache: 'npm'
          
          - name: Install dependencies
            run: npm ci
          
          - name: Run linting
            run: npm run lint
          
          - name: Run type checking
            run: npm run type-check
          
          - name: Run unit tests
            run: npm run test
          
          - name: Run E2E tests
            run: npm run test:e2e
    
      security:
        runs-on: ubuntu-latest
        steps:
          - uses: actions/checkout@v4
          
          - name: Run security audit
            run: npm audit --audit-level=high
          
          - name: Run dependency check
            uses: dependency-check/Dependency-Check_Action@main
            with:
              project: 'ai-novel-assistant'
              path: '.'
              format: 'ALL'
    
      build:
        needs: [test, security]
        strategy:
          matrix:
            os: [windows-latest, macos-latest, ubuntu-latest]
        runs-on: ${{ matrix.os }}
        
        steps:
          - uses: actions/checkout@v4
          
          - name: Setup Node.js
            uses: actions/setup-node@v4
            with:
              node-version: '20'
              cache: 'npm'
          
          - name: Install dependencies
            run: npm ci
          
          - name: Build application
            run: npm run build
          
          - name: Build Electron app
            run: npm run build:all
            env:
              GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
              CSC_LINK: ${{ secrets.CSC_LINK }}
              CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
              APPLE_ID: ${{ secrets.APPLE_ID }}
              APPLE_ID_PASS: ${{ secrets.APPLE_ID_PASS }}
          
          - name: Upload artifacts
            uses: actions/upload-artifact@v4
            with:
              name: dist-${{ matrix.os }}
              path: dist/
    
      release:
        needs: build
        runs-on: ubuntu-latest
        if: startsWith(github.ref, 'refs/tags/v')
        
        steps:
          - uses: actions/checkout@v4
          
          - name: Download all artifacts
            uses: actions/download-artifact@v4
          
          - name: Create Release
            uses: softprops/action-gh-release@v1
            with:
              files: |
                dist-windows-latest/*.exe
                dist-macos-latest/*.dmg
                dist-ubuntu-latest/*.AppImage
                dist-ubuntu-latest/*.deb
              draft: false
              prerelease: false
            env:
              GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    ```
    
    ### Step 3: 本地构建脚本
    ```bash
    #!/bin/bash
    # scripts/build.sh - 本地构建脚本
    
    set -e
    
    echo "🚀 开始构建 AI小说助手..."
    
    # 检查Node.js版本
    NODE_VERSION=$(node -v)
    echo "Node.js版本: $NODE_VERSION"
    
    # 清理旧的构建文件
    echo "🧹 清理旧的构建文件..."
    rm -rf dist dist-main dist-renderer
    
    # 安装依赖
    echo "📦 安装依赖..."
    npm ci
    
    # 运行测试
    echo "🧪 运行测试..."
    npm run test
    npm run lint
    npm run type-check
    
    # 构建应用
    echo "🔨 构建应用..."
    npm run build
    
    # 根据平台构建
    case "$OSTYPE" in
      msys*|cygwin*|win*)
        echo "🪟 构建 Windows 版本..."
        npm run build:win
        ;;
      darwin*)
        echo "🍎 构建 macOS 版本..."
        npm run build:mac
        ;;
      linux*)
        echo "🐧 构建 Linux 版本..."
        npm run build:linux
        ;;
      *)
        echo "❓ 未知平台，构建所有版本..."
        npm run build:all
        ;;
    esac
    
    echo "✅ 构建完成！"
    echo "📁 构建文件位于 dist/ 目录"
    ```
    
    ### Step 4: 版本管理脚本
    ```javascript
    // scripts/version.js - 版本管理脚本
    const fs = require('fs')
    const path = require('path')
    const { execSync } = require('child_process')
    
    class VersionManager {
      constructor() {
        this.packagePath = path.join(__dirname, '../package.json')
        this.package = JSON.parse(fs.readFileSync(this.packagePath, 'utf8'))
      }
      
      // 获取当前版本
      getCurrentVersion() {
        return this.package.version
      }
      
      // 更新版本号
      updateVersion(type = 'patch') {
        const currentVersion = this.getCurrentVersion()
        const [major, minor, patch] = currentVersion.split('.').map(Number)
        
        let newVersion
        switch (type) {
          case 'major':
            newVersion = `${major + 1}.0.0`
            break
          case 'minor':
            newVersion = `${major}.${minor + 1}.0`
            break
          case 'patch':
          default:
            newVersion = `${major}.${minor}.${patch + 1}`
            break
        }
        
        this.package.version = newVersion
        fs.writeFileSync(this.packagePath, JSON.stringify(this.package, null, 2))
        
        return newVersion
      }
      
      // 创建Git标签
      createGitTag(version) {
        try {
          execSync(`git add package.json`)
          execSync(`git commit -m "chore: bump version to ${version}"`)
          execSync(`git tag -a v${version} -m "Release version ${version}"`)
          console.log(`✅ 创建标签 v${version}`)
        } catch (error) {
          console.error('❌ 创建Git标签失败:', error.message)
        }
      }
      
      // 发布版本
      release(type = 'patch') {
        const newVersion = this.updateVersion(type)
        console.log(`📦 版本更新: ${this.getCurrentVersion()} -> ${newVersion}`)
        
        this.createGitTag(newVersion)
        
        console.log(`🚀 版本 ${newVersion} 发布完成！`)
        console.log('💡 推送到远程仓库: git push origin main --tags')
        
        return newVersion
      }
    }
    
    // 命令行使用
    if (require.main === module) {
      const versionManager = new VersionManager()
      const type = process.argv[2] || 'patch'
      
      versionManager.release(type)
    }
    
    module.exports = VersionManager
    ```
    
    ### Step 5: 部署监控
    ```typescript
    // src/utils/deploymentMonitor.ts
    export class DeploymentMonitor {
      private metrics: Map<string, any> = new Map()
      
      // 记录构建开始
      startBuild(buildId: string) {
        this.metrics.set(buildId, {
          startTime: Date.now(),
          status: 'building',
          platform: process.platform,
          nodeVersion: process.version
        })
      }
      
      // 记录构建完成
      completeBuild(buildId: string, success: boolean, error?: Error) {
        const metric = this.metrics.get(buildId)
        if (metric) {
          metric.endTime = Date.now()
          metric.duration = metric.endTime - metric.startTime
          metric.status = success ? 'success' : 'failed'
          metric.error = error?.message
          
          this.reportMetrics(buildId, metric)
        }
      }
      
      // 上报指标
      private reportMetrics(buildId: string, metric: any) {
        console.log(`📊 构建指标 [${buildId}]:`, {
          平台: metric.platform,
          状态: metric.status,
          耗时: `${(metric.duration / 1000).toFixed(2)}s`,
          Node版本: metric.nodeVersion
        })
        
        // 可以集成到监控系统
        // this.sendToMonitoringSystem(metric)
      }
      
      // 健康检查
      healthCheck() {
        return {
          timestamp: new Date().toISOString(),
          platform: process.platform,
          nodeVersion: process.version,
          memoryUsage: process.memoryUsage(),
          uptime: process.uptime()
        }
      }
    }
    ```
  </process>

  <criteria>
    ## 质量标准
    
    ### 构建性能
    - ✅ 单平台构建时间 < 10分钟
    - ✅ 全平台构建时间 < 30分钟
    - ✅ 构建成功率 > 95%
    - ✅ 包体积控制在合理范围
    
    ### 部署质量
    - ✅ 自动化测试覆盖率 > 80%
    - ✅ 安全扫描无高危漏洞
    - ✅ 代码签名验证通过
    - ✅ 多平台兼容性验证
    
    ### 运维效率
    - ✅ 部署流程完全自动化
    - ✅ 回滚时间 < 5分钟
    - ✅ 监控告警及时准确
    - ✅ 文档完整易懂
    
    ### 安全合规
    - ✅ 代码签名证书管理安全
    - ✅ 敏感信息加密存储
    - ✅ 访问权限控制严格
    - ✅ 审计日志完整
  </criteria>
</execution>
