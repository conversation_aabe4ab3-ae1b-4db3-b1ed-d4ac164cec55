<execution>
  <constraint>
    ## 技术约束
    - **数据库类型**：必须使用SQLite 3.x，不允许外部数据库
    - **ORM框架**：必须使用Prisma 5.x，确保类型安全
    - **文件大小**：数据库文件大小控制在合理范围内
    - **并发限制**：SQLite的并发写入限制，需要合理设计
    - **内存使用**：数据库连接和缓存的内存控制
    - **备份策略**：支持数据库文件的备份和恢复
  </constraint>

  <rule>
    ## 设计规则
    - **主键规则**：所有表必须有主键，优先使用自增整数
    - **外键约束**：必须定义外键关系，确保引用完整性
    - **索引规则**：查询频繁的字段必须建立索引
    - **命名规范**：表名、字段名使用snake_case命名
    - **数据类型**：选择合适的数据类型，避免浪费空间
    - **事务使用**：复杂操作必须使用事务保证一致性
  </rule>

  <guideline>
    ## 设计指导原则
    - **性能优先**：优化查询性能，合理使用索引
    - **数据完整性**：通过约束确保数据质量
    - **可扩展性**：为未来功能预留扩展空间
    - **维护性**：清晰的表结构，完善的注释
    - **安全性**：敏感数据的加密存储
    - **备份恢复**：支持数据的备份和恢复
  </guideline>

  <process>
    ## 数据库开发流程
    
    ### Step 1: Prisma初始化
    ```bash
    # 安装Prisma
    npm install prisma @prisma/client
    npm install -D prisma
    
    # 初始化Prisma
    npx prisma init
    ```
    
    ### Step 2: Schema设计
    ```prisma
    // prisma/schema.prisma
    generator client {
      provider = "prisma-client-js"
    }
    
    datasource db {
      provider = "sqlite"
      url      = "file:./ai-novel.db"
    }
    
    // 项目表
    model Project {
      id              Int       @id @default(autoincrement())
      title           String
      genre           String?
      theme           String?
      style           String?
      totalChapters   Int       @default(1)
      wordsPerChapter Int       @default(3000)
      createdAt       DateTime  @default(now())
      updatedAt       DateTime  @updatedAt
      
      // 关联关系
      outlines        Outline[]
      chapters        Chapter[]
      characters      Character[]
      
      @@map("projects")
    }
    
    // 大纲表
    model Outline {
      id           Int      @id @default(autoincrement())
      projectId    Int
      title        String?
      coreTheme    String?
      storySummary String?
      worldSetting String?
      createdAt    DateTime @default(now())
      updatedAt    DateTime @updatedAt
      
      // 外键关系
      project      Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
      
      @@map("outlines")
    }
    
    // 章节表
    model Chapter {
      id            Int      @id @default(autoincrement())
      projectId     Int
      chapterNumber Int
      title         String?
      summary       String?
      content       String?
      wordCount     Int      @default(0)
      status        String   @default("draft") // draft, completed
      createdAt     DateTime @default(now())
      updatedAt     DateTime @updatedAt
      
      // 外键关系
      project       Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
      
      // 多对多关系
      characters    ChapterCharacter[]
      
      @@unique([projectId, chapterNumber])
      @@map("chapters")
    }
    
    // 角色表
    model Character {
      id           Int      @id @default(autoincrement())
      projectId    Int
      name         String
      roleType     String?  // 主角/重要角色/配角/反派/龙套
      identity     String?
      personality  String?
      background   String?
      appearance   String?
      createdAt    DateTime @default(now())
      updatedAt    DateTime @updatedAt
      
      // 外键关系
      project      Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
      
      // 多对多关系
      chapters     ChapterCharacter[]
      relationships CharacterRelationship[] @relation("CharacterRelationships")
      relatedTo    CharacterRelationship[] @relation("RelatedCharacters")
      
      @@map("characters")
    }
    
    // 章节角色关联表
    model ChapterCharacter {
      id          Int       @id @default(autoincrement())
      chapterId   Int
      characterId Int
      createdAt   DateTime  @default(now())
      
      chapter     Chapter   @relation(fields: [chapterId], references: [id], onDelete: Cascade)
      character   Character @relation(fields: [characterId], references: [id], onDelete: Cascade)
      
      @@unique([chapterId, characterId])
      @@map("chapter_characters")
    }
    
    // 角色关系表
    model CharacterRelationship {
      id            Int       @id @default(autoincrement())
      projectId     Int
      character1Id  Int
      character2Id  Int
      relationship  String
      description   String?
      createdAt     DateTime  @default(now())
      
      character1    Character @relation("CharacterRelationships", fields: [character1Id], references: [id], onDelete: Cascade)
      character2    Character @relation("RelatedCharacters", fields: [character2Id], references: [id], onDelete: Cascade)
      
      @@map("character_relationships")
    }
    ```
    
    ### Step 3: 数据访问层
    ```typescript
    // src/database/client.ts
    import { PrismaClient } from '@prisma/client'
    import { join } from 'path'
    import { app } from 'electron'
    
    class DatabaseClient {
      private static instance: PrismaClient
      
      static getInstance(): PrismaClient {
        if (!DatabaseClient.instance) {
          const dbPath = join(app.getPath('userData'), 'ai-novel.db')
          DatabaseClient.instance = new PrismaClient({
            datasources: {
              db: {
                url: `file:${dbPath}`
              }
            }
          })
        }
        return DatabaseClient.instance
      }
      
      static async disconnect() {
        if (DatabaseClient.instance) {
          await DatabaseClient.instance.$disconnect()
        }
      }
    }
    
    export const db = DatabaseClient.getInstance()
    ```
    
    ### Step 4: 数据服务层
    ```typescript
    // src/database/services/ProjectService.ts
    import { db } from '../client'
    import type { Project, Prisma } from '@prisma/client'
    
    export class ProjectService {
      // 创建项目
      async createProject(data: Prisma.ProjectCreateInput): Promise<Project> {
        return await db.project.create({
          data,
          include: {
            outlines: true,
            chapters: true,
            characters: true
          }
        })
      }
      
      // 获取项目列表
      async getProjects(): Promise<Project[]> {
        return await db.project.findMany({
          include: {
            _count: {
              select: {
                chapters: true,
                characters: true
              }
            }
          },
          orderBy: {
            updatedAt: 'desc'
          }
        })
      }
      
      // 获取项目详情
      async getProjectById(id: number): Promise<Project | null> {
        return await db.project.findUnique({
          where: { id },
          include: {
            outlines: true,
            chapters: {
              orderBy: { chapterNumber: 'asc' }
            },
            characters: true
          }
        })
      }
      
      // 更新项目
      async updateProject(id: number, data: Prisma.ProjectUpdateInput): Promise<Project> {
        return await db.project.update({
          where: { id },
          data
        })
      }
      
      // 删除项目
      async deleteProject(id: number): Promise<void> {
        await db.project.delete({
          where: { id }
        })
      }
    }
    ```
    
    ### Step 5: 数据库迁移
    ```bash
    # 生成迁移文件
    npx prisma migrate dev --name init
    
    # 应用迁移
    npx prisma migrate deploy
    
    # 生成Prisma客户端
    npx prisma generate
    ```
    
    ### Step 6: 性能优化
    ```sql
    -- 创建索引
    CREATE INDEX idx_chapters_project_id ON chapters(project_id);
    CREATE INDEX idx_chapters_status ON chapters(status);
    CREATE INDEX idx_characters_project_id ON characters(project_id);
    CREATE INDEX idx_characters_role_type ON characters(role_type);
    
    -- 启用WAL模式
    PRAGMA journal_mode=WAL;
    
    -- 优化设置
    PRAGMA synchronous=NORMAL;
    PRAGMA cache_size=10000;
    PRAGMA temp_store=memory;
    ```
  </process>

  <criteria>
    ## 质量标准
    
    ### 数据完整性
    - ✅ 所有外键约束正确定义
    - ✅ 数据类型选择合理
    - ✅ 必要字段设置非空约束
    - ✅ 唯一约束防止重复数据
    
    ### 性能指标
    - ✅ 查询响应时间 < 100ms
    - ✅ 批量操作使用事务
    - ✅ 索引覆盖常用查询
    - ✅ 数据库文件大小合理
    
    ### 可维护性
    - ✅ Schema文件结构清晰
    - ✅ 迁移脚本完整
    - ✅ 数据访问层封装良好
    - ✅ 错误处理完善
    
    ### 扩展性
    - ✅ 表结构支持功能扩展
    - ✅ 预留字段满足未来需求
    - ✅ 关系设计支持复杂查询
    - ✅ 版本管理机制完善
  </criteria>
</execution>
