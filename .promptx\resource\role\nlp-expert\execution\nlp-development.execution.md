<execution>
  <constraint>
    ## 技术约束
    - **处理速度**：文本处理响应时间 < 2秒
    - **内存使用**：NLP处理内存占用 < 200MB
    - **准确率要求**：内容质量评估准确率 > 85%
    - **语言支持**：主要支持中文，兼容英文
    - **模型大小**：本地模型文件 < 100MB
    - **并发处理**：支持多个文本同时处理
  </constraint>

  <rule>
    ## 处理规则
    - **质量优先**：所有生成内容必须经过质量检查
    - **风格一致**：同一项目内容必须保持风格统一
    - **上下文相关**：处理时必须考虑上下文信息
    - **用户偏好**：根据用户设置调整处理策略
    - **安全过滤**：过滤不当内容和敏感信息
    - **版本控制**：保留处理前后的版本对比
  </rule>

  <guideline>
    ## 处理指导原则
    - **渐进优化**：分步骤进行内容优化，避免过度修改
    - **保持原意**：优化过程中保持原始内容的核心意思
    - **个性化处理**：根据角色特点个性化语言风格
    - **情感增强**：适当增强情感表达的真实性
    - **可读性优先**：确保处理后内容的可读性
    - **用户控制**：提供用户可控的处理参数
  </guideline>

  <process>
    ## NLP处理流程
    
    ### Step 1: 文本预处理
    ```typescript
    // src/nlp/preprocessor.ts
    export class TextPreprocessor {
      // 文本清理
      cleanText(text: string): string {
        return text
          .replace(/\s+/g, ' ')  // 规范化空白字符
          .replace(/[""]/g, '"') // 统一引号
          .replace(/['']/g, "'") // 统一撇号
          .trim()
      }
      
      // 分句处理
      splitSentences(text: string): string[] {
        const sentences = text.split(/[。！？.!?]/)
        return sentences
          .map(s => s.trim())
          .filter(s => s.length > 0)
      }
      
      // 分段处理
      splitParagraphs(text: string): string[] {
        return text.split(/\n\s*\n/)
          .map(p => p.trim())
          .filter(p => p.length > 0)
      }
    }
    ```
    
    ### Step 2: 内容质量评估
    ```typescript
    // src/nlp/qualityAssessment.ts
    export interface QualityMetrics {
      fluency: number        // 流畅度 0-100
      coherence: number      // 连贯性 0-100
      creativity: number     // 创意性 0-100
      emotion: number        // 情感丰富度 0-100
      aiLikeness: number     // AI味浓度 0-100
      overallScore: number   // 综合评分 0-100
    }
    
    export class QualityAssessment {
      // 评估文本质量
      async assessQuality(text: string, context?: any): Promise<QualityMetrics> {
        const fluency = this.assessFluency(text)
        const coherence = this.assessCoherence(text, context)
        const creativity = this.assessCreativity(text)
        const emotion = this.assessEmotion(text)
        const aiLikeness = this.assessAILikeness(text)
        
        const overallScore = this.calculateOverallScore({
          fluency, coherence, creativity, emotion, aiLikeness
        })
        
        return {
          fluency, coherence, creativity, emotion, aiLikeness, overallScore
        }
      }
      
      // 流畅度评估
      private assessFluency(text: string): number {
        // 检查语法错误、句式结构等
        const sentences = this.preprocessor.splitSentences(text)
        let score = 100
        
        // 检查句子长度分布
        const avgLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length
        if (avgLength > 50 || avgLength < 10) score -= 10
        
        // 检查重复词汇
        const words = text.match(/[\u4e00-\u9fa5]+/g) || []
        const uniqueWords = new Set(words)
        const repetitionRate = 1 - (uniqueWords.size / words.length)
        if (repetitionRate > 0.3) score -= 20
        
        return Math.max(0, score)
      }
      
      // AI味检测
      private assessAILikeness(text: string): number {
        const aiPatterns = [
          /总的来说|总而言之|综上所述/g,
          /值得注意的是|需要指出的是/g,
          /首先.*其次.*最后/g,
          /一方面.*另一方面/g,
          /不仅.*而且/g
        ]
        
        let aiScore = 0
        aiPatterns.forEach(pattern => {
          const matches = text.match(pattern)
          if (matches) aiScore += matches.length * 10
        })
        
        return Math.min(100, aiScore)
      }
    }
    ```
    
    ### Step 3: 降AI味处理
    ```typescript
    // src/nlp/aiFlavorReducer.ts
    export class AIFlavorReducer {
      private replacementPatterns = new Map([
        ['总的来说', ['总之', '简而言之', '说到底']],
        ['值得注意的是', ['要知道', '关键是', '重要的是']],
        ['首先.*其次.*最后', ['先是.*接着.*然后']],
        ['不仅.*而且', ['既.*又', '不但.*还']]
      ])
      
      // 降AI味处理
      async reduceAIFlavor(text: string): Promise<string> {
        let processedText = text
        
        // 替换机械化表达
        processedText = this.replacePatterns(processedText)
        
        // 增加情感色彩
        processedText = this.enhanceEmotion(processedText)
        
        // 个性化表达
        processedText = this.personalizeExpression(processedText)
        
        return processedText
      }
      
      private replacePatterns(text: string): string {
        let result = text
        
        this.replacementPatterns.forEach((replacements, pattern) => {
          const regex = new RegExp(pattern, 'g')
          result = result.replace(regex, () => {
            const randomIndex = Math.floor(Math.random() * replacements.length)
            return replacements[randomIndex]
          })
        })
        
        return result
      }
      
      private enhanceEmotion(text: string): string {
        // 添加情感词汇和语气词
        const emotionEnhancers = ['啊', '呢', '吧', '嘛', '呀']
        const sentences = text.split(/[。！？]/)
        
        return sentences.map(sentence => {
          if (sentence.trim() && Math.random() < 0.3) {
            const enhancer = emotionEnhancers[Math.floor(Math.random() * emotionEnhancers.length)]
            return sentence + enhancer
          }
          return sentence
        }).join('。')
      }
    }
    ```
    
    ### Step 4: 内容分析
    ```typescript
    // src/nlp/contentAnalyzer.ts
    export interface AnalysisResult {
      plotSummary: string
      keyEvents: string[]
      characters: CharacterMention[]
      items: ItemMention[]
      emotions: EmotionAnalysis[]
      suggestions: string[]
    }
    
    export class ContentAnalyzer {
      // 分析章节内容
      async analyzeContent(text: string, context: any): Promise<AnalysisResult> {
        const plotSummary = await this.extractPlotSummary(text)
        const keyEvents = this.extractKeyEvents(text)
        const characters = this.extractCharacters(text, context.characters)
        const items = this.extractItems(text)
        const emotions = this.analyzeEmotions(text)
        const suggestions = this.generateSuggestions(text, context)
        
        return {
          plotSummary, keyEvents, characters, items, emotions, suggestions
        }
      }
      
      private extractKeyEvents(text: string): string[] {
        // 提取关键事件
        const eventPatterns = [
          /突然.*?[。！？]/g,
          /忽然.*?[。！？]/g,
          /终于.*?[。！？]/g,
          /决定.*?[。！？]/g
        ]
        
        const events: string[] = []
        eventPatterns.forEach(pattern => {
          const matches = text.match(pattern)
          if (matches) events.push(...matches)
        })
        
        return events.slice(0, 5) // 返回前5个关键事件
      }
      
      private extractCharacters(text: string, knownCharacters: any[]): CharacterMention[] {
        const mentions: CharacterMention[] = []
        
        knownCharacters.forEach(character => {
          const regex = new RegExp(character.name, 'g')
          const matches = text.match(regex)
          if (matches) {
            mentions.push({
              characterId: character.id,
              name: character.name,
              mentionCount: matches.length,
              contexts: this.extractMentionContexts(text, character.name)
            })
          }
        })
        
        return mentions
      }
    }
    ```
    
    ### Step 5: 提示词优化
    ```typescript
    // src/nlp/promptOptimizer.ts
    export class PromptOptimizer {
      // 优化生成提示词
      optimizePrompt(basePrompt: string, context: any, userPreferences: any): string {
        let optimizedPrompt = basePrompt
        
        // 添加上下文信息
        optimizedPrompt = this.addContextInfo(optimizedPrompt, context)
        
        // 添加风格指导
        optimizedPrompt = this.addStyleGuidance(optimizedPrompt, userPreferences)
        
        // 添加质量约束
        optimizedPrompt = this.addQualityConstraints(optimizedPrompt)
        
        return optimizedPrompt
      }
      
      private addContextInfo(prompt: string, context: any): string {
        const contextInfo = `
        
上下文信息：
- 小说类型：${context.genre}
- 写作风格：${context.style}
- 当前章节：第${context.chapterNumber}章
- 主要角色：${context.characters.map(c => c.name).join('、')}
- 前情提要：${context.previousSummary}
        `
        
        return prompt + contextInfo
      }
      
      private addQualityConstraints(prompt: string): string {
        const constraints = `
        
质量要求：
1. 语言自然流畅，避免机械化表达
2. 情感真实细腻，符合人物性格
3. 情节合理连贯，与前文呼应
4. 描写生动具体，富有画面感
5. 对话个性鲜明，符合角色特点
        `
        
        return prompt + constraints
      }
    }
    ```
  </process>

  <criteria>
    ## 质量标准
    
    ### 处理效果
    - ✅ 降AI味效果显著，自然度提升 > 30%
    - ✅ 内容质量评估准确率 > 85%
    - ✅ 处理后内容保持原意不变
    - ✅ 风格一致性得到保证
    
    ### 性能指标
    - ✅ 文本处理速度 < 2秒
    - ✅ 内存使用 < 200MB
    - ✅ 并发处理能力 > 5个文本
    - ✅ 系统稳定性 > 99%
    
    ### 用户体验
    - ✅ 处理结果满意度 > 80%
    - ✅ 操作界面简洁易用
    - ✅ 处理过程可视化
    - ✅ 支持撤销和重做
    
    ### 技术指标
    - ✅ 算法准确性验证
    - ✅ 边界情况处理
    - ✅ 错误恢复机制
    - ✅ 性能监控完善
  </criteria>
</execution>
