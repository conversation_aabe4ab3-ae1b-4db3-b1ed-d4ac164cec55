<execution>
  <constraint>
    ## 技术约束
    - **渲染性能**：60fps流畅渲染，响应时间 < 16ms
    - **内存使用**：可视化组件内存占用 < 100MB
    - **数据量限制**：支持1000+节点的关系图渲染
    - **浏览器兼容**：支持Chromium内核，兼容主流分辨率
    - **文件大小**：图表库总大小 < 2MB
    - **加载时间**：组件初始化时间 < 1秒
  </constraint>

  <rule>
    ## 开发规则
    - **性能优先**：所有可视化组件必须经过性能测试
    - **响应式设计**：必须适配不同屏幕尺寸
    - **主题一致**：必须符合Glassmorphism设计风格
    - **交互标准**：提供标准的交互反馈
    - **数据安全**：可视化过程中保护用户数据
    - **错误处理**：优雅处理数据异常和渲染错误
  </rule>

  <guideline>
    ## 设计指导原则
    - **用户体验优先**：直观易懂的可视化设计
    - **渐进增强**：基础功能稳定，高级功能可选
    - **模块化设计**：可复用的可视化组件
    - **配置灵活**：支持用户自定义配置
    - **无障碍访问**：支持键盘操作和屏幕阅读器
    - **文档完善**：清晰的API文档和使用示例
  </guideline>

  <process>
    ## 可视化开发流程
    
    ### Step 1: 基础图表组件
    ```typescript
    // src/components/charts/BaseChart.vue
    <template>
      <div class="chart-container glass-card">
        <canvas 
          ref="chartCanvas"
          :width="width"
          :height="height"
          @mousedown="onMouseDown"
          @mousemove="onMouseMove"
          @mouseup="onMouseUp"
          @wheel="onWheel"
        />
      </div>
    </template>
    
    <script setup lang="ts">
    import { ref, onMounted, onUnmounted, watch } from 'vue'
    
    interface ChartProps {
      data: any[]
      width: number
      height: number
      options?: ChartOptions
    }
    
    const props = defineProps<ChartProps>()
    const chartCanvas = ref<HTMLCanvasElement>()
    let ctx: CanvasRenderingContext2D
    let animationId: number
    
    onMounted(() => {
      ctx = chartCanvas.value!.getContext('2d')!
      initChart()
      startRenderLoop()
    })
    
    onUnmounted(() => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    })
    
    function initChart() {
      // 初始化图表
      setupCanvas()
      processData()
      render()
    }
    
    function setupCanvas() {
      const dpr = window.devicePixelRatio || 1
      const canvas = chartCanvas.value!
      
      canvas.width = props.width * dpr
      canvas.height = props.height * dpr
      canvas.style.width = props.width + 'px'
      canvas.style.height = props.height + 'px'
      
      ctx.scale(dpr, dpr)
    }
    
    function render() {
      ctx.clearRect(0, 0, props.width, props.height)
      
      // 绘制背景
      drawBackground()
      
      // 绘制数据
      drawData()
      
      // 绘制交互元素
      drawInteractions()
    }
    </script>
    ```
    
    ### Step 2: 人物关系图组件
    ```typescript
    // src/components/charts/RelationshipGraph.vue
    <template>
      <div class="relationship-graph">
        <div class="graph-controls">
          <button @click="resetLayout">重置布局</button>
          <button @click="togglePhysics">{{ physicsEnabled ? '停止' : '开始' }}物理模拟</button>
          <select v-model="selectedLayout" @change="changeLayout">
            <option value="force">力导向布局</option>
            <option value="circular">环形布局</option>
            <option value="hierarchical">层次布局</option>
          </select>
        </div>
        
        <canvas 
          ref="graphCanvas"
          class="graph-canvas"
          @mousedown="onMouseDown"
          @mousemove="onMouseMove"
          @mouseup="onMouseUp"
          @wheel="onWheel"
          @click="onClick"
        />
        
        <div v-if="selectedNode" class="node-info glass-card">
          <h3>{{ selectedNode.name }}</h3>
          <p>{{ selectedNode.description }}</p>
          <div class="relationships">
            <div v-for="rel in selectedNode.relationships" :key="rel.id">
              {{ rel.type }}: {{ rel.target.name }}
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <script setup lang="ts">
    import { ref, computed, onMounted } from 'vue'
    import { ForceDirectedLayout } from '@/utils/layouts/ForceDirectedLayout'
    import { CircularLayout } from '@/utils/layouts/CircularLayout'
    import { HierarchicalLayout } from '@/utils/layouts/HierarchicalLayout'
    
    interface Node {
      id: string
      name: string
      x: number
      y: number
      vx: number
      vy: number
      radius: number
      color: string
      relationships: Relationship[]
    }
    
    interface Edge {
      source: Node
      target: Node
      type: string
      strength: number
    }
    
    const props = defineProps<{
      nodes: Node[]
      edges: Edge[]
    }>()
    
    const graphCanvas = ref<HTMLCanvasElement>()
    const selectedNode = ref<Node | null>(null)
    const selectedLayout = ref('force')
    const physicsEnabled = ref(true)
    
    let ctx: CanvasRenderingContext2D
    let layout: any
    let isDragging = false
    let dragNode: Node | null = null
    
    onMounted(() => {
      ctx = graphCanvas.value!.getContext('2d')!
      initGraph()
      startSimulation()
    })
    
    function initGraph() {
      setupCanvas()
      initLayout()
      render()
    }
    
    function initLayout() {
      switch (selectedLayout.value) {
        case 'force':
          layout = new ForceDirectedLayout(props.nodes, props.edges)
          break
        case 'circular':
          layout = new CircularLayout(props.nodes, props.edges)
          break
        case 'hierarchical':
          layout = new HierarchicalLayout(props.nodes, props.edges)
          break
      }
    }
    
    function startSimulation() {
      function animate() {
        if (physicsEnabled.value) {
          layout.update()
        }
        render()
        requestAnimationFrame(animate)
      }
      animate()
    }
    
    function render() {
      ctx.clearRect(0, 0, graphCanvas.value!.width, graphCanvas.value!.height)
      
      // 绘制边
      props.edges.forEach(edge => {
        drawEdge(edge)
      })
      
      // 绘制节点
      props.nodes.forEach(node => {
        drawNode(node)
      })
      
      // 绘制选中状态
      if (selectedNode.value) {
        drawSelectedNode(selectedNode.value)
      }
    }
    
    function drawNode(node: Node) {
      ctx.save()
      
      // 绘制节点圆形
      ctx.beginPath()
      ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2)
      ctx.fillStyle = node.color
      ctx.fill()
      
      // 绘制边框
      ctx.strokeStyle = '#ffffff'
      ctx.lineWidth = 2
      ctx.stroke()
      
      // 绘制文字
      ctx.fillStyle = '#333333'
      ctx.font = '12px Arial'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText(node.name, node.x, node.y)
      
      ctx.restore()
    }
    
    function drawEdge(edge: Edge) {
      ctx.save()
      
      ctx.beginPath()
      ctx.moveTo(edge.source.x, edge.source.y)
      ctx.lineTo(edge.target.x, edge.target.y)
      
      // 根据关系类型设置样式
      switch (edge.type) {
        case 'family':
          ctx.strokeStyle = '#ff6b6b'
          ctx.lineWidth = 3
          break
        case 'friend':
          ctx.strokeStyle = '#4ecdc4'
          ctx.lineWidth = 2
          break
        case 'enemy':
          ctx.strokeStyle = '#ff9f43'
          ctx.lineWidth = 2
          ctx.setLineDash([5, 5])
          break
        default:
          ctx.strokeStyle = '#95a5a6'
          ctx.lineWidth = 1
      }
      
      ctx.stroke()
      ctx.restore()
    }
    
    // 鼠标事件处理
    function onMouseDown(event: MouseEvent) {
      const rect = graphCanvas.value!.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      const node = findNodeAt(x, y)
      if (node) {
        isDragging = true
        dragNode = node
        selectedNode.value = node
      }
    }
    
    function onMouseMove(event: MouseEvent) {
      if (isDragging && dragNode) {
        const rect = graphCanvas.value!.getBoundingClientRect()
        dragNode.x = event.clientX - rect.left
        dragNode.y = event.clientY - rect.top
      }
    }
    
    function onMouseUp() {
      isDragging = false
      dragNode = null
    }
    
    function findNodeAt(x: number, y: number): Node | null {
      return props.nodes.find(node => {
        const dx = x - node.x
        const dy = y - node.y
        return Math.sqrt(dx * dx + dy * dy) <= node.radius
      }) || null
    }
    </script>
    ```
    
    ### Step 3: 统计图表组件
    ```typescript
    // src/components/charts/StatisticsChart.vue
    <template>
      <div class="statistics-chart glass-card">
        <div class="chart-header">
          <h3>{{ title }}</h3>
          <div class="chart-controls">
            <select v-model="chartType" @change="updateChart">
              <option value="line">折线图</option>
              <option value="bar">柱状图</option>
              <option value="pie">饼图</option>
            </select>
          </div>
        </div>
        
        <div class="chart-content">
          <canvas ref="chartCanvas" />
        </div>
        
        <div class="chart-legend">
          <div 
            v-for="(item, index) in legendItems" 
            :key="index"
            class="legend-item"
            @click="toggleSeries(index)"
          >
            <span 
              class="legend-color" 
              :style="{ backgroundColor: item.color }"
            />
            <span class="legend-label">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </template>
    
    <script setup lang="ts">
    import { ref, computed, watch, onMounted } from 'vue'
    import Chart from 'chart.js/auto'
    
    interface ChartData {
      labels: string[]
      datasets: {
        label: string
        data: number[]
        backgroundColor?: string | string[]
        borderColor?: string
        borderWidth?: number
      }[]
    }
    
    const props = defineProps<{
      title: string
      data: ChartData
      options?: any
    }>()
    
    const chartCanvas = ref<HTMLCanvasElement>()
    const chartType = ref('line')
    let chartInstance: Chart | null = null
    
    const legendItems = computed(() => {
      return props.data.datasets.map(dataset => ({
        label: dataset.label,
        color: dataset.backgroundColor || dataset.borderColor || '#3498db'
      }))
    })
    
    onMounted(() => {
      createChart()
    })
    
    watch(() => props.data, () => {
      updateChart()
    }, { deep: true })
    
    function createChart() {
      if (chartInstance) {
        chartInstance.destroy()
      }
      
      const ctx = chartCanvas.value!.getContext('2d')!
      
      chartInstance = new Chart(ctx, {
        type: chartType.value as any,
        data: props.data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false // 使用自定义图例
            },
            tooltip: {
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              titleColor: '#ffffff',
              bodyColor: '#ffffff',
              borderColor: 'rgba(255, 255, 255, 0.2)',
              borderWidth: 1
            }
          },
          scales: chartType.value !== 'pie' ? {
            x: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#ffffff'
              }
            },
            y: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                color: '#ffffff'
              }
            }
          } : {},
          ...props.options
        }
      })
    }
    
    function updateChart() {
      createChart()
    }
    
    function toggleSeries(index: number) {
      if (chartInstance) {
        const meta = chartInstance.getDatasetMeta(index)
        meta.hidden = !meta.hidden
        chartInstance.update()
      }
    }
    </script>
    ```
  </process>

  <criteria>
    ## 质量标准
    
    ### 性能指标
    - ✅ 渲染帧率 ≥ 60fps
    - ✅ 初始化时间 < 1秒
    - ✅ 内存使用 < 100MB
    - ✅ 支持1000+节点渲染
    
    ### 视觉效果
    - ✅ 符合Glassmorphism设计风格
    - ✅ 支持明暗主题切换
    - ✅ 动画效果流畅自然
    - ✅ 颜色搭配和谐美观
    
    ### 交互体验
    - ✅ 响应时间 < 100ms
    - ✅ 支持多种交互方式
    - ✅ 提供清晰的视觉反馈
    - ✅ 错误状态处理完善
    
    ### 兼容性
    - ✅ 支持不同屏幕分辨率
    - ✅ 适配高DPI显示器
    - ✅ 跨平台渲染一致
    - ✅ 无障碍访问支持
  </criteria>
</execution>
