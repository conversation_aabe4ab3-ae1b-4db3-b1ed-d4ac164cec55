<thought>
  <exploration>
    ## 数据库设计深度思考
    
    ### 小说创作应用的数据特征
    - **层次结构**：项目 → 大纲 → 章节的清晰层次关系
    - **复杂关系**：角色间的多对多关系网络
    - **版本管理**：内容的历史版本和修改记录
    - **元数据丰富**：创建时间、修改时间、状态标记等
    
    ### SQLite在桌面应用中的优势
    - **零配置**：无需安装数据库服务器，文件即数据库
    - **轻量级**：小巧的库文件，适合桌面应用
    - **ACID特性**：完整的事务支持，数据安全可靠
    - **跨平台**：Windows、macOS、Linux完全兼容
    
    ### 数据模型设计考量
    - **范式平衡**：在规范化和查询性能间找到平衡
    - **索引策略**：基于查询模式设计合理的索引
    - **扩展性**：为未来功能预留字段和表结构
    - **性能优化**：避免N+1查询，合理使用JOIN
  </exploration>
  
  <reasoning>
    ## 架构决策推理
    
    ### 为什么选择SQLite + Prisma
    - **开发效率**：Prisma提供类型安全的ORM，减少SQL错误
    - **维护性**：Schema文件统一管理，迁移脚本自动生成
    - **性能**：SQLite针对读多写少的场景优化良好
    - **部署简单**：单文件数据库，无需额外配置
    
    ### 表结构设计原则
    - **主键策略**：使用自增整数主键，性能优于UUID
    - **外键约束**：确保引用完整性，防止数据孤岛
    - **索引设计**：基于查询频率和数据量设计索引
    - **字段类型**：选择合适的数据类型，节省存储空间
    
    ### 性能优化策略
    - **查询优化**：使用EXPLAIN分析查询计划
    - **批量操作**：大量数据操作使用事务批处理
    - **连接池**：合理配置连接池大小
    - **缓存策略**：热点数据的内存缓存
  </reasoning>
  
  <challenge>
    ## 数据库设计挑战
    
    ### 复杂关系建模
    - **挑战**：角色关系的多对多建模复杂
    - **解决**：使用中间表存储关系类型和描述
    - **优化**：关系查询的性能优化
    
    ### 版本管理
    - **挑战**：内容版本历史的存储和查询
    - **解决**：设计版本表，记录变更历史
    - **平衡**：存储空间和查询性能的平衡
    
    ### 数据迁移
    - **挑战**：应用升级时的数据库结构变更
    - **解决**：Prisma迁移机制，自动生成迁移脚本
    - **安全**：迁移前的数据备份和回滚机制
  </challenge>
  
  <plan>
    ## 数据库开发计划
    
    ### Phase 1: 核心表设计
    - 项目表：存储小说基本信息
    - 大纲表：存储小说大纲内容
    - 章节表：存储章节详细信息
    - 角色表：存储角色档案
    
    ### Phase 2: 关系表设计
    - 角色关系表：角色间的复杂关系
    - 章节角色关联表：章节中出现的角色
    - 版本历史表：内容变更记录
    
    ### Phase 3: 配置表设计
    - AI配置表：API密钥和模型配置
    - 提示词模板表：用户自定义模板
    - 用户设置表：应用配置信息
    
    ### Phase 4: 性能优化
    - 索引优化：基于查询模式优化索引
    - 查询优化：复杂查询的性能调优
    - 缓存策略：热点数据的缓存机制
  </plan>
</thought>
