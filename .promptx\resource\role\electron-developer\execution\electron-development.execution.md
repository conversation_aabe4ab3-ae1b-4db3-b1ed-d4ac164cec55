<execution>
  <constraint>
    ## 技术约束
    - **Electron版本**：使用Electron 28.x稳定版本
    - **Node.js版本**：内置Node.js 20.x，不依赖外部Node.js环境
    - **安全要求**：必须禁用nodeIntegration，启用contextIsolation
    - **内存限制**：主进程内存使用<100MB，渲染进程<500MB
    - **启动时间**：应用启动时间<3秒，窗口显示<1秒
    - **包大小限制**：安装包大小<200MB，解压后<500MB
  </constraint>

  <rule>
    ## 开发规则
    - **安全第一**：所有IPC通信必须通过contextBridge暴露
    - **类型安全**：所有API接口必须有完整的TypeScript类型定义
    - **错误处理**：所有异步操作必须有完善的错误处理机制
    - **资源管理**：及时释放不用的资源，避免内存泄漏
    - **平台兼容**：代码必须在Windows、macOS、Linux上正常运行
    - **测试覆盖**：核心功能必须有单元测试和集成测试
  </rule>

  <guideline>
    ## 开发指导原则
    - **渐进式开发**：先实现核心功能，再添加高级特性
    - **模块化设计**：主进程和渲染进程都采用模块化架构
    - **配置驱动**：通过配置文件控制应用行为，便于定制
    - **日志记录**：完善的日志系统，便于问题排查
    - **用户体验**：注重应用的响应性和交互流畅性
    - **文档完善**：代码注释清晰，API文档完整
  </guideline>

  <process>
    ## Electron开发流程
    
    ### Step 1: 项目初始化
    ```bash
    # 创建项目结构
    mkdir ai-novel-assistant
    cd ai-novel-assistant
    
    # 初始化package.json
    npm init -y
    
    # 安装核心依赖
    npm install electron@28.x vue@3.x typescript@5.x vite@5.x
    npm install @types/node @vitejs/plugin-vue electron-builder
    ```
    
    ### Step 2: 配置文件设置
    ```typescript
    // vite.config.ts - Vite配置
    import { defineConfig } from 'vite'
    import vue from '@vitejs/plugin-vue'
    import { resolve } from 'path'
    
    export default defineConfig({
      plugins: [vue()],
      base: './',
      build: {
        outDir: 'dist-renderer',
        emptyOutDir: true
      },
      resolve: {
        alias: {
          '@': resolve(__dirname, 'src/renderer')
        }
      }
    })
    ```
    
    ### Step 3: 主进程开发
    ```typescript
    // src/main/index.ts - 主进程入口
    import { app, BrowserWindow, ipcMain } from 'electron'
    import { join } from 'path'
    
    class MainProcess {
      private mainWindow: BrowserWindow | null = null
      
      constructor() {
        this.initializeApp()
      }
      
      private initializeApp() {
        app.whenReady().then(() => {
          this.createMainWindow()
          this.setupIPC()
        })
        
        app.on('window-all-closed', () => {
          if (process.platform !== 'darwin') {
            app.quit()
          }
        })
      }
      
      private createMainWindow() {
        this.mainWindow = new BrowserWindow({
          width: 1200,
          height: 800,
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: join(__dirname, 'preload.js')
          }
        })
        
        // 加载渲染进程
        if (process.env.NODE_ENV === 'development') {
          this.mainWindow.loadURL('http://localhost:5173')
        } else {
          this.mainWindow.loadFile('dist-renderer/index.html')
        }
      }
      
      private setupIPC() {
        // 设置IPC处理器
        ipcMain.handle('app:getVersion', () => {
          return app.getVersion()
        })
      }
    }
    
    new MainProcess()
    ```
    
    ### Step 4: 预加载脚本
    ```typescript
    // src/main/preload.ts - 安全桥接
    import { contextBridge, ipcRenderer } from 'electron'
    
    // 暴露安全的API到渲染进程
    contextBridge.exposeInMainWorld('electronAPI', {
      // 应用信息
      getVersion: () => ipcRenderer.invoke('app:getVersion'),
      
      // 文件操作
      openFile: () => ipcRenderer.invoke('file:open'),
      saveFile: (data: any) => ipcRenderer.invoke('file:save', data),
      
      // 数据库操作
      dbQuery: (sql: string, params?: any[]) => 
        ipcRenderer.invoke('db:query', sql, params),
      
      // AI服务
      aiGenerate: (prompt: string, config: any) => 
        ipcRenderer.invoke('ai:generate', prompt, config)
    })
    
    // 类型定义
    declare global {
      interface Window {
        electronAPI: {
          getVersion: () => Promise<string>
          openFile: () => Promise<string>
          saveFile: (data: any) => Promise<void>
          dbQuery: (sql: string, params?: any[]) => Promise<any>
          aiGenerate: (prompt: string, config: any) => Promise<string>
        }
      }
    }
    ```
    
    ### Step 5: 渲染进程开发
    ```vue
    <!-- src/renderer/App.vue -->
    <template>
      <div id="app" class="glass-container">
        <router-view />
      </div>
    </template>
    
    <script setup lang="ts">
    import { onMounted } from 'vue'
    import { useAppStore } from '@/stores/app'
    
    const appStore = useAppStore()
    
    onMounted(async () => {
      // 初始化应用
      const version = await window.electronAPI.getVersion()
      appStore.setVersion(version)
    })
    </script>
    ```
    
    ### Step 6: 构建配置
    ```json
    // electron-builder配置
    {
      "build": {
        "appId": "com.ainovel.assistant",
        "productName": "AI小说助手",
        "directories": {
          "output": "dist"
        },
        "files": [
          "dist-main/**/*",
          "dist-renderer/**/*",
          "node_modules/**/*"
        ],
        "win": {
          "target": "nsis",
          "icon": "resources/icon.ico"
        },
        "mac": {
          "target": "dmg",
          "icon": "resources/icon.icns"
        },
        "linux": {
          "target": "AppImage",
          "icon": "resources/icon.png"
        }
      }
    }
    ```
  </process>

  <criteria>
    ## 质量标准
    
    ### 功能完整性
    - ✅ 所有核心功能正常工作
    - ✅ IPC通信稳定可靠
    - ✅ 数据持久化正常
    - ✅ AI服务集成成功
    
    ### 性能指标
    - ✅ 启动时间 < 3秒
    - ✅ 内存使用合理
    - ✅ CPU占用 < 10%
    - ✅ 响应时间 < 200ms
    
    ### 安全性
    - ✅ 禁用Node.js集成
    - ✅ 启用上下文隔离
    - ✅ CSP策略配置
    - ✅ 安全审计通过
    
    ### 兼容性
    - ✅ Windows 10/11支持
    - ✅ macOS 10.15+支持
    - ✅ Ubuntu 18.04+支持
    - ✅ 多分辨率适配
  </criteria>
</execution>
