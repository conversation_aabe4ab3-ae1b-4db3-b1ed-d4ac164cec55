<execution>
  <constraint>
    ## 技术约束
    - **文档大小**：支持最大100万字的文档编辑
    - **响应时间**：输入延迟 < 50ms，滚动流畅度 > 60fps
    - **内存使用**：编辑器内存占用 < 200MB
    - **自动保存**：每30秒或500字符变更自动保存
    - **版本历史**：保留最近100个版本记录
    - **兼容性**：支持Chromium内核，适配高DPI显示
  </constraint>

  <rule>
    ## 开发规则
    - **数据安全**：所有编辑操作必须有撤销机制
    - **性能优先**：大文档编辑必须保持流畅性
    - **用户体验**：编辑界面必须简洁直观
    - **快捷键支持**：必须支持标准的编辑快捷键
    - **格式控制**：避免复杂格式，专注内容编辑
    - **版本管理**：重要操作必须记录版本历史
  </rule>

  <guideline>
    ## 设计指导原则
    - **写作优先**：界面设计服务于写作体验
    - **减少干扰**：最小化不必要的界面元素
    - **快速响应**：所有操作都要有即时反馈
    - **容错设计**：提供完善的错误恢复机制
    - **渐进增强**：基础功能稳定，高级功能可选
    - **无障碍访问**：支持键盘操作和屏幕阅读器
  </guideline>

  <process>
    ## 富文本编辑器开发流程
    
    ### Step 1: 编辑器选型和集成
    ```typescript
    // src/components/editor/NovelEditor.vue
    <template>
      <div class="novel-editor glass-card">
        <div class="editor-toolbar">
          <div class="toolbar-group">
            <button @click="undo" :disabled="!canUndo">撤销</button>
            <button @click="redo" :disabled="!canRedo">重做</button>
          </div>
          
          <div class="toolbar-group">
            <button @click="toggleBold" :class="{ active: isBold }">粗体</button>
            <button @click="toggleItalic" :class="{ active: isItalic }">斜体</button>
          </div>
          
          <div class="toolbar-group">
            <span class="word-count">字数: {{ wordCount }}</span>
            <span class="save-status">{{ saveStatus }}</span>
          </div>
        </div>
        
        <div class="editor-container">
          <div 
            ref="editorElement"
            class="editor-content"
            contenteditable="true"
            @input="onInput"
            @keydown="onKeyDown"
            @paste="onPaste"
            @focus="onFocus"
            @blur="onBlur"
          />
        </div>
        
        <div class="editor-footer">
          <div class="chapter-navigation">
            <button @click="previousChapter">上一章</button>
            <span>第{{ currentChapter }}章</span>
            <button @click="nextChapter">下一章</button>
          </div>
        </div>
      </div>
    </template>
    
    <script setup lang="ts">
    import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
    import { useEditorStore } from '@/stores/editor'
    import { debounce } from '@/utils/debounce'
    
    interface EditorProps {
      content: string
      chapterId: string
      readonly?: boolean
    }
    
    const props = defineProps<EditorProps>()
    const emit = defineEmits<{
      'update:content': [content: string]
      'save': [content: string]
      'chapter-change': [direction: 'prev' | 'next']
    }>()
    
    const editorStore = useEditorStore()
    const editorElement = ref<HTMLDivElement>()
    
    // 编辑器状态
    const wordCount = ref(0)
    const saveStatus = ref('已保存')
    const canUndo = ref(false)
    const canRedo = ref(false)
    const isBold = ref(false)
    const isItalic = ref(false)
    const currentChapter = ref(1)
    
    // 自动保存
    const autoSave = debounce((content: string) => {
      saveContent(content)
    }, 2000)
    
    onMounted(() => {
      initEditor()
      setupKeyboardShortcuts()
    })
    
    function initEditor() {
      if (editorElement.value) {
        editorElement.value.innerHTML = props.content
        updateWordCount()
        updateToolbarState()
      }
    }
    
    function onInput(event: Event) {
      const content = editorElement.value?.innerHTML || ''
      updateWordCount()
      updateSaveStatus('未保存')
      
      emit('update:content', content)
      autoSave(content)
    }
    
    function onKeyDown(event: KeyboardEvent) {
      // 处理快捷键
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'z':
            event.preventDefault()
            if (event.shiftKey) {
              redo()
            } else {
              undo()
            }
            break
          case 'b':
            event.preventDefault()
            toggleBold()
            break
          case 'i':
            event.preventDefault()
            toggleItalic()
            break
          case 's':
            event.preventDefault()
            saveContent(editorElement.value?.innerHTML || '')
            break
        }
      }
    }
    
    function updateWordCount() {
      const text = editorElement.value?.textContent || ''
      wordCount.value = text.replace(/\s/g, '').length
    }
    
    function updateSaveStatus(status: string) {
      saveStatus.value = status
    }
    
    async function saveContent(content: string) {
      try {
        await editorStore.saveChapter(props.chapterId, content)
        updateSaveStatus('已保存')
        emit('save', content)
      } catch (error) {
        updateSaveStatus('保存失败')
        console.error('保存失败:', error)
      }
    }
    </script>
    ```
    
    ### Step 2: 版本控制系统
    ```typescript
    // src/stores/editor.ts
    import { defineStore } from 'pinia'
    
    interface EditorVersion {
      id: string
      content: string
      timestamp: Date
      wordCount: number
      description?: string
    }
    
    interface EditorState {
      currentContent: string
      versions: EditorVersion[]
      currentVersionIndex: number
      isModified: boolean
    }
    
    export const useEditorStore = defineStore('editor', {
      state: (): EditorState => ({
        currentContent: '',
        versions: [],
        currentVersionIndex: -1,
        isModified: false
      }),
      
      getters: {
        canUndo: (state) => state.currentVersionIndex > 0,
        canRedo: (state) => state.currentVersionIndex < state.versions.length - 1,
        currentVersion: (state) => state.versions[state.currentVersionIndex]
      },
      
      actions: {
        // 保存新版本
        saveVersion(content: string, description?: string) {
          const version: EditorVersion = {
            id: Date.now().toString(),
            content,
            timestamp: new Date(),
            wordCount: content.replace(/\s/g, '').length,
            description
          }
          
          // 如果当前不在最新版本，删除后续版本
          if (this.currentVersionIndex < this.versions.length - 1) {
            this.versions = this.versions.slice(0, this.currentVersionIndex + 1)
          }
          
          this.versions.push(version)
          this.currentVersionIndex = this.versions.length - 1
          this.currentContent = content
          this.isModified = false
          
          // 限制版本数量
          if (this.versions.length > 100) {
            this.versions = this.versions.slice(-100)
            this.currentVersionIndex = this.versions.length - 1
          }
        },
        
        // 撤销
        undo(): string | null {
          if (this.canUndo) {
            this.currentVersionIndex--
            this.currentContent = this.currentVersion.content
            return this.currentContent
          }
          return null
        },
        
        // 重做
        redo(): string | null {
          if (this.canRedo) {
            this.currentVersionIndex++
            this.currentContent = this.currentVersion.content
            return this.currentContent
          }
          return null
        },
        
        // 保存章节
        async saveChapter(chapterId: string, content: string) {
          // 保存到数据库
          await window.electronAPI.dbQuery(
            'UPDATE chapters SET content = ?, word_count = ?, updated_at = ? WHERE id = ?',
            [content, content.replace(/\s/g, '').length, new Date().toISOString(), chapterId]
          )
          
          // 保存版本
          this.saveVersion(content, '自动保存')
        }
      }
    })
    ```
    
    ### Step 3: 性能优化
    ```typescript
    // src/components/editor/VirtualEditor.vue
    <template>
      <div class="virtual-editor" ref="containerRef">
        <div 
          class="virtual-content"
          :style="{ height: totalHeight + 'px' }"
        >
          <div 
            class="visible-content"
            :style="{ transform: `translateY(${offsetY}px)` }"
          >
            <div
              v-for="item in visibleItems"
              :key="item.index"
              class="content-block"
              :style="{ height: itemHeight + 'px' }"
            >
              {{ item.content }}
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <script setup lang="ts">
    import { ref, computed, onMounted, onUnmounted } from 'vue'
    
    interface ContentItem {
      index: number
      content: string
      height: number
    }
    
    const props = defineProps<{
      items: ContentItem[]
      itemHeight: number
    }>()
    
    const containerRef = ref<HTMLDivElement>()
    const scrollTop = ref(0)
    const containerHeight = ref(0)
    
    // 计算可见区域
    const visibleRange = computed(() => {
      const start = Math.floor(scrollTop.value / props.itemHeight)
      const end = Math.min(
        start + Math.ceil(containerHeight.value / props.itemHeight) + 1,
        props.items.length
      )
      return { start, end }
    })
    
    const visibleItems = computed(() => {
      const { start, end } = visibleRange.value
      return props.items.slice(start, end).map((item, index) => ({
        ...item,
        index: start + index
      }))
    })
    
    const totalHeight = computed(() => {
      return props.items.length * props.itemHeight
    })
    
    const offsetY = computed(() => {
      return visibleRange.value.start * props.itemHeight
    })
    
    onMounted(() => {
      if (containerRef.value) {
        containerHeight.value = containerRef.value.clientHeight
        containerRef.value.addEventListener('scroll', onScroll)
        window.addEventListener('resize', onResize)
      }
    })
    
    onUnmounted(() => {
      if (containerRef.value) {
        containerRef.value.removeEventListener('scroll', onScroll)
      }
      window.removeEventListener('resize', onResize)
    })
    
    function onScroll() {
      if (containerRef.value) {
        scrollTop.value = containerRef.value.scrollTop
      }
    }
    
    function onResize() {
      if (containerRef.value) {
        containerHeight.value = containerRef.value.clientHeight
      }
    }
    </script>
    ```
    
    ### Step 4: 自动保存机制
    ```typescript
    // src/utils/autoSave.ts
    export class AutoSaveManager {
      private saveTimer: NodeJS.Timeout | null = null
      private lastSaveTime = 0
      private saveInterval = 30000 // 30秒
      private changeThreshold = 500 // 500字符变更
      private lastContent = ''
      private changeCount = 0
      
      constructor(
        private saveCallback: (content: string) => Promise<void>,
        private options: {
          interval?: number
          threshold?: number
        } = {}
      ) {
        this.saveInterval = options.interval || this.saveInterval
        this.changeThreshold = options.threshold || this.changeThreshold
      }
      
      // 内容变更时调用
      onContentChange(content: string) {
        const changeSize = Math.abs(content.length - this.lastContent.length)
        this.changeCount += changeSize
        this.lastContent = content
        
        // 如果变更超过阈值，立即保存
        if (this.changeCount >= this.changeThreshold) {
          this.saveNow(content)
          return
        }
        
        // 否则延迟保存
        this.scheduleSave(content)
      }
      
      // 立即保存
      async saveNow(content: string) {
        this.clearSaveTimer()
        
        try {
          await this.saveCallback(content)
          this.lastSaveTime = Date.now()
          this.changeCount = 0
        } catch (error) {
          console.error('自动保存失败:', error)
        }
      }
      
      // 计划保存
      private scheduleSave(content: string) {
        this.clearSaveTimer()
        
        this.saveTimer = setTimeout(() => {
          this.saveNow(content)
        }, this.saveInterval)
      }
      
      // 清除保存定时器
      private clearSaveTimer() {
        if (this.saveTimer) {
          clearTimeout(this.saveTimer)
          this.saveTimer = null
        }
      }
      
      // 销毁
      destroy() {
        this.clearSaveTimer()
      }
    }
    ```
  </process>

  <criteria>
    ## 质量标准
    
    ### 性能指标
    - ✅ 输入延迟 < 50ms
    - ✅ 滚动帧率 ≥ 60fps
    - ✅ 内存使用 < 200MB
    - ✅ 支持100万字文档
    
    ### 功能完整性
    - ✅ 基础编辑功能完整
    - ✅ 自动保存机制可靠
    - ✅ 版本控制功能正常
    - ✅ 快捷键支持完善
    
    ### 用户体验
    - ✅ 界面简洁直观
    - ✅ 操作响应及时
    - ✅ 错误处理完善
    - ✅ 无障碍访问支持
    
    ### 数据安全
    - ✅ 自动保存可靠
    - ✅ 版本历史完整
    - ✅ 数据恢复机制
    - ✅ 异常情况处理
  </criteria>
</execution>
