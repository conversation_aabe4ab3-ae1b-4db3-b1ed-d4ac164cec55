<execution>
  <constraint>
    ## 性能约束
    - **启动时间**：冷启动 < 3秒，热启动 < 1秒
    - **内存使用**：主进程 < 100MB，渲染进程 < 500MB
    - **CPU使用**：空闲时 < 5%，工作时 < 30%
    - **响应时间**：UI交互 < 100ms，数据操作 < 500ms
    - **帧率要求**：UI动画 ≥ 60fps，滚动流畅度 ≥ 60fps
    - **文件大小**：应用包体积 < 200MB
  </constraint>

  <rule>
    ## 优化规则
    - **性能优先**：关键路径的性能优化优先级最高
    - **数据驱动**：所有优化决策必须基于性能数据
    - **用户体验**：优化不能影响用户的正常使用
    - **可测量性**：所有优化效果必须可量化验证
    - **可维护性**：优化代码必须保持良好的可维护性
    - **兼容性**：优化不能破坏跨平台兼容性
  </rule>

  <guideline>
    ## 优化指导原则
    - **渐进优化**：从影响最大的问题开始优化
    - **预防为主**：在设计阶段就考虑性能问题
    - **持续监控**：建立长期的性能监控机制
    - **工具辅助**：充分利用性能分析工具
    - **团队协作**：性能优化需要全团队参与
    - **文档记录**：记录优化过程和效果
  </guideline>

  <process>
    ## 性能优化实施流程
    
    ### Step 1: 性能监控系统
    ```typescript
    // src/utils/performanceMonitor.ts
    export class PerformanceMonitor {
      private metrics: Map<string, PerformanceMetric> = new Map()
      private observers: PerformanceObserver[] = []
      
      constructor() {
        this.initializeObservers()
      }
      
      // 初始化性能观察器
      private initializeObservers() {
        // 监控导航性能
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordNavigationMetric(entry as PerformanceNavigationTiming)
          }
        })
        navObserver.observe({ entryTypes: ['navigation'] })
        this.observers.push(navObserver)
        
        // 监控资源加载性能
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordResourceMetric(entry as PerformanceResourceTiming)
          }
        })
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)
        
        // 监控长任务
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordLongTask(entry)
          }
        })
        longTaskObserver.observe({ entryTypes: ['longtask'] })
        this.observers.push(longTaskObserver)
      }
      
      // 记录自定义性能指标
      recordMetric(name: string, value: number, unit: string = 'ms') {
        const metric: PerformanceMetric = {
          name,
          value,
          unit,
          timestamp: Date.now(),
          type: 'custom'
        }
        
        this.metrics.set(name, metric)
        this.reportMetric(metric)
      }
      
      // 测量函数执行时间
      async measureFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
        const startTime = performance.now()
        try {
          const result = await fn()
          const duration = performance.now() - startTime
          this.recordMetric(name, duration)
          return result
        } catch (error) {
          const duration = performance.now() - startTime
          this.recordMetric(`${name}_error`, duration)
          throw error
        }
      }
      
      // 监控内存使用
      recordMemoryUsage() {
        if ('memory' in performance) {
          const memory = (performance as any).memory
          this.recordMetric('memory_used', memory.usedJSHeapSize / 1024 / 1024, 'MB')
          this.recordMetric('memory_total', memory.totalJSHeapSize / 1024 / 1024, 'MB')
          this.recordMetric('memory_limit', memory.jsHeapSizeLimit / 1024 / 1024, 'MB')
        }
      }
      
      // 监控FPS
      monitorFPS() {
        let lastTime = performance.now()
        let frames = 0
        
        const measureFPS = () => {
          frames++
          const currentTime = performance.now()
          
          if (currentTime >= lastTime + 1000) {
            const fps = Math.round((frames * 1000) / (currentTime - lastTime))
            this.recordMetric('fps', fps, 'fps')
            
            frames = 0
            lastTime = currentTime
          }
          
          requestAnimationFrame(measureFPS)
        }
        
        requestAnimationFrame(measureFPS)
      }
      
      // 获取性能报告
      getPerformanceReport(): PerformanceReport {
        const metrics = Array.from(this.metrics.values())
        
        return {
          timestamp: new Date().toISOString(),
          metrics,
          summary: this.generateSummary(metrics),
          recommendations: this.generateRecommendations(metrics)
        }
      }
      
      private generateSummary(metrics: PerformanceMetric[]): PerformanceSummary {
        const memoryMetrics = metrics.filter(m => m.name.startsWith('memory_'))
        const fpsMetrics = metrics.filter(m => m.name === 'fps')
        const responseMetrics = metrics.filter(m => m.name.includes('response'))
        
        return {
          averageMemoryUsage: this.calculateAverage(memoryMetrics),
          averageFPS: this.calculateAverage(fpsMetrics),
          averageResponseTime: this.calculateAverage(responseMetrics),
          totalMetrics: metrics.length
        }
      }
      
      private generateRecommendations(metrics: PerformanceMetric[]): string[] {
        const recommendations: string[] = []
        
        // 内存使用建议
        const memoryUsage = metrics.find(m => m.name === 'memory_used')
        if (memoryUsage && memoryUsage.value > 500) {
          recommendations.push('内存使用过高，建议检查内存泄漏')
        }
        
        // FPS建议
        const fps = metrics.find(m => m.name === 'fps')
        if (fps && fps.value < 30) {
          recommendations.push('帧率过低，建议优化渲染性能')
        }
        
        // 响应时间建议
        const responseTime = metrics.find(m => m.name.includes('response'))
        if (responseTime && responseTime.value > 1000) {
          recommendations.push('响应时间过长，建议优化数据处理逻辑')
        }
        
        return recommendations
      }
    }
    
    interface PerformanceMetric {
      name: string
      value: number
      unit: string
      timestamp: number
      type: 'navigation' | 'resource' | 'custom' | 'longtask'
    }
    
    interface PerformanceReport {
      timestamp: string
      metrics: PerformanceMetric[]
      summary: PerformanceSummary
      recommendations: string[]
    }
    
    interface PerformanceSummary {
      averageMemoryUsage: number
      averageFPS: number
      averageResponseTime: number
      totalMetrics: number
    }
    ```
    
    ### Step 2: 内存优化工具
    ```typescript
    // src/utils/memoryOptimizer.ts
    export class MemoryOptimizer {
      private objectPools: Map<string, ObjectPool> = new Map()
      private weakRefs: Set<WeakRef<any>> = new Set()
      private cleanupTimer: NodeJS.Timeout | null = null
      
      constructor() {
        this.startCleanupTimer()
      }
      
      // 对象池管理
      createObjectPool<T>(name: string, factory: () => T, maxSize: number = 100): ObjectPool<T> {
        const pool = new ObjectPool(factory, maxSize)
        this.objectPools.set(name, pool)
        return pool
      }
      
      getObjectPool<T>(name: string): ObjectPool<T> | undefined {
        return this.objectPools.get(name) as ObjectPool<T>
      }
      
      // 弱引用管理
      createWeakRef<T extends object>(obj: T): WeakRef<T> {
        const weakRef = new WeakRef(obj)
        this.weakRefs.add(weakRef)
        return weakRef
      }
      
      // 内存清理
      cleanup() {
        // 清理失效的弱引用
        for (const weakRef of this.weakRefs) {
          if (weakRef.deref() === undefined) {
            this.weakRefs.delete(weakRef)
          }
        }
        
        // 清理对象池
        for (const pool of this.objectPools.values()) {
          pool.cleanup()
        }
        
        // 强制垃圾回收（仅在开发环境）
        if (process.env.NODE_ENV === 'development' && global.gc) {
          global.gc()
        }
      }
      
      // 内存使用分析
      analyzeMemoryUsage(): MemoryAnalysis {
        const usage = process.memoryUsage()
        
        return {
          heapUsed: usage.heapUsed / 1024 / 1024,
          heapTotal: usage.heapTotal / 1024 / 1024,
          external: usage.external / 1024 / 1024,
          rss: usage.rss / 1024 / 1024,
          objectPools: this.objectPools.size,
          weakRefs: this.weakRefs.size,
          timestamp: Date.now()
        }
      }
      
      // 内存泄漏检测
      detectMemoryLeaks(): MemoryLeakReport {
        const analysis = this.analyzeMemoryUsage()
        const leaks: MemoryLeak[] = []
        
        // 检查堆内存增长
        if (analysis.heapUsed > 500) {
          leaks.push({
            type: 'heap_growth',
            severity: 'high',
            description: '堆内存使用过高',
            recommendation: '检查是否存在内存泄漏'
          })
        }
        
        // 检查对象池大小
        for (const [name, pool] of this.objectPools) {
          if (pool.size > pool.maxSize * 0.8) {
            leaks.push({
              type: 'object_pool_overflow',
              severity: 'medium',
              description: `对象池 ${name} 接近容量上限`,
              recommendation: '增加对象池大小或优化对象复用'
            })
          }
        }
        
        return {
          timestamp: new Date().toISOString(),
          analysis,
          leaks,
          recommendations: this.generateMemoryRecommendations(leaks)
        }
      }
      
      private startCleanupTimer() {
        this.cleanupTimer = setInterval(() => {
          this.cleanup()
        }, 60000) // 每分钟清理一次
      }
      
      private generateMemoryRecommendations(leaks: MemoryLeak[]): string[] {
        const recommendations: string[] = []
        
        if (leaks.some(leak => leak.type === 'heap_growth')) {
          recommendations.push('定期检查和清理不再使用的对象')
          recommendations.push('使用WeakMap和WeakSet存储临时引用')
        }
        
        if (leaks.some(leak => leak.type === 'object_pool_overflow')) {
          recommendations.push('优化对象池的使用策略')
          recommendations.push('考虑增加对象池的容量')
        }
        
        return recommendations
      }
      
      destroy() {
        if (this.cleanupTimer) {
          clearInterval(this.cleanupTimer)
        }
        this.cleanup()
      }
    }
    
    class ObjectPool<T> {
      private pool: T[] = []
      private inUse: Set<T> = new Set()
      
      constructor(
        private factory: () => T,
        public readonly maxSize: number = 100
      ) {}
      
      acquire(): T {
        let obj = this.pool.pop()
        if (!obj) {
          obj = this.factory()
        }
        this.inUse.add(obj)
        return obj
      }
      
      release(obj: T) {
        if (this.inUse.has(obj)) {
          this.inUse.delete(obj)
          if (this.pool.length < this.maxSize) {
            this.pool.push(obj)
          }
        }
      }
      
      get size(): number {
        return this.pool.length + this.inUse.size
      }
      
      cleanup() {
        // 清理超出容量的对象
        while (this.pool.length > this.maxSize / 2) {
          this.pool.pop()
        }
      }
    }
    ```
    
    ### Step 3: 渲染性能优化
    ```typescript
    // src/utils/renderOptimizer.ts
    export class RenderOptimizer {
      private rafId: number | null = null
      private pendingUpdates: Set<() => void> = new Set()
      private isUpdating = false
      
      // 批量更新DOM
      batchUpdate(updateFn: () => void) {
        this.pendingUpdates.add(updateFn)
        
        if (!this.isUpdating) {
          this.isUpdating = true
          this.rafId = requestAnimationFrame(() => {
            this.flushUpdates()
          })
        }
      }
      
      private flushUpdates() {
        const updates = Array.from(this.pendingUpdates)
        this.pendingUpdates.clear()
        this.isUpdating = false
        
        // 执行所有待更新的操作
        for (const update of updates) {
          try {
            update()
          } catch (error) {
            console.error('渲染更新失败:', error)
          }
        }
      }
      
      // 防抖函数
      debounce<T extends (...args: any[]) => any>(
        func: T,
        wait: number
      ): (...args: Parameters<T>) => void {
        let timeout: NodeJS.Timeout
        
        return (...args: Parameters<T>) => {
          clearTimeout(timeout)
          timeout = setTimeout(() => func.apply(this, args), wait)
        }
      }
      
      // 节流函数
      throttle<T extends (...args: any[]) => any>(
        func: T,
        limit: number
      ): (...args: Parameters<T>) => void {
        let inThrottle: boolean
        
        return (...args: Parameters<T>) => {
          if (!inThrottle) {
            func.apply(this, args)
            inThrottle = true
            setTimeout(() => inThrottle = false, limit)
          }
        }
      }
      
      // 虚拟滚动优化
      createVirtualScroller(options: VirtualScrollerOptions) {
        return new VirtualScroller(options)
      }
      
      // 图片懒加载
      createLazyLoader() {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              if (img.dataset.src) {
                img.src = img.dataset.src
                img.removeAttribute('data-src')
                observer.unobserve(img)
              }
            }
          })
        })
        
        return {
          observe: (img: HTMLImageElement) => observer.observe(img),
          disconnect: () => observer.disconnect()
        }
      }
      
      destroy() {
        if (this.rafId) {
          cancelAnimationFrame(this.rafId)
        }
        this.pendingUpdates.clear()
      }
    }
    
    class VirtualScroller {
      private container: HTMLElement
      private items: any[]
      private itemHeight: number
      private visibleCount: number
      private scrollTop = 0
      private startIndex = 0
      private endIndex = 0
      
      constructor(options: VirtualScrollerOptions) {
        this.container = options.container
        this.items = options.items
        this.itemHeight = options.itemHeight
        this.visibleCount = Math.ceil(this.container.clientHeight / this.itemHeight) + 2
        
        this.setupScrollListener()
        this.render()
      }
      
      private setupScrollListener() {
        this.container.addEventListener('scroll', () => {
          this.scrollTop = this.container.scrollTop
          this.updateVisibleRange()
          this.render()
        })
      }
      
      private updateVisibleRange() {
        this.startIndex = Math.floor(this.scrollTop / this.itemHeight)
        this.endIndex = Math.min(this.startIndex + this.visibleCount, this.items.length)
      }
      
      private render() {
        const visibleItems = this.items.slice(this.startIndex, this.endIndex)
        
        // 清空容器
        this.container.innerHTML = ''
        
        // 创建占位元素
        const spacerTop = document.createElement('div')
        spacerTop.style.height = `${this.startIndex * this.itemHeight}px`
        this.container.appendChild(spacerTop)
        
        // 渲染可见项目
        visibleItems.forEach((item, index) => {
          const element = this.createItemElement(item, this.startIndex + index)
          this.container.appendChild(element)
        })
        
        // 创建底部占位元素
        const spacerBottom = document.createElement('div')
        const remainingHeight = (this.items.length - this.endIndex) * this.itemHeight
        spacerBottom.style.height = `${remainingHeight}px`
        this.container.appendChild(spacerBottom)
      }
      
      private createItemElement(item: any, index: number): HTMLElement {
        const element = document.createElement('div')
        element.style.height = `${this.itemHeight}px`
        element.textContent = `Item ${index}: ${item.name || item.toString()}`
        return element
      }
      
      updateItems(newItems: any[]) {
        this.items = newItems
        this.updateVisibleRange()
        this.render()
      }
    }
    ```
  </process>

  <criteria>
    ## 质量标准
    
    ### 性能指标
    - ✅ 启动时间达到目标要求
    - ✅ 内存使用控制在合理范围
    - ✅ CPU使用率保持在低水平
    - ✅ UI响应时间满足用户期望
    
    ### 监控完整性
    - ✅ 关键性能指标全覆盖
    - ✅ 实时监控数据准确
    - ✅ 性能异常及时发现
    - ✅ 历史数据完整保存
    
    ### 优化效果
    - ✅ 优化前后对比明显
    - ✅ 用户体验显著提升
    - ✅ 系统稳定性增强
    - ✅ 资源使用更加合理
    
    ### 可维护性
    - ✅ 优化代码结构清晰
    - ✅ 性能监控易于使用
    - ✅ 问题定位快速准确
    - ✅ 优化策略可持续
  </criteria>
</execution>
