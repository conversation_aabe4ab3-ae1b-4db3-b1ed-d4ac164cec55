{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-30T13:45:47.951Z", "updatedAt": "2025-07-30T13:45:48.056Z", "resourceCount": 48}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:45:47.956Z", "updatedAt": "2025-07-30T13:45:47.956Z", "scannedAt": "2025-07-30T13:45:47.956Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "ai-integration-process", "source": "project", "protocol": "execution", "name": "Ai Integration Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-integration-expert/execution/ai-integration-process.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:47.958Z", "updatedAt": "2025-07-30T13:45:47.958Z", "scannedAt": "2025-07-30T13:45:47.958Z", "path": "role/ai-integration-expert/execution/ai-integration-process.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:47.961Z", "updatedAt": "2025-07-30T13:45:47.961Z", "scannedAt": "2025-07-30T13:45:47.961Z", "path": "role/ai-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "ai-novel-architect", "source": "project", "protocol": "role", "name": "Ai Novel Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-architect/ai-novel-architect.role.md", "metadata": {"createdAt": "2025-07-30T13:45:47.962Z", "updatedAt": "2025-07-30T13:45:47.962Z", "scannedAt": "2025-07-30T13:45:47.962Z", "path": "role/ai-novel-architect/ai-novel-architect.role.md"}}, {"id": "ai-novel-development", "source": "project", "protocol": "execution", "name": "Ai Novel Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-architect/execution/ai-novel-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:47.964Z", "updatedAt": "2025-07-30T13:45:47.964Z", "scannedAt": "2025-07-30T13:45:47.964Z", "path": "role/ai-novel-architect/execution/ai-novel-development.execution.md"}}, {"id": "ai-novel-thinking", "source": "project", "protocol": "thought", "name": "Ai Novel Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-architect/thought/ai-novel-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:47.966Z", "updatedAt": "2025-07-30T13:45:47.966Z", "scannedAt": "2025-07-30T13:45:47.966Z", "path": "role/ai-novel-architect/thought/ai-novel-thinking.thought.md"}}, {"id": "ai-novel-writer", "source": "project", "protocol": "role", "name": "Ai Novel Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-writer/ai-novel-writer.role.md", "metadata": {"createdAt": "2025-07-30T13:45:47.968Z", "updatedAt": "2025-07-30T13:45:47.968Z", "scannedAt": "2025-07-30T13:45:47.967Z", "path": "role/ai-novel-writer/ai-novel-writer.role.md"}}, {"id": "creative-writing-process", "source": "project", "protocol": "execution", "name": "Creative Writing Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-writer/execution/creative-writing-process.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:47.969Z", "updatedAt": "2025-07-30T13:45:47.969Z", "scannedAt": "2025-07-30T13:45:47.969Z", "path": "role/ai-novel-writer/execution/creative-writing-process.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-writer/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:47.971Z", "updatedAt": "2025-07-30T13:45:47.971Z", "scannedAt": "2025-07-30T13:45:47.971Z", "path": "role/ai-novel-writer/thought/creative-writing-thinking.thought.md"}}, {"id": "data-visualization-expert", "source": "project", "protocol": "role", "name": "Data Visualization Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/data-visualization-expert/data-visualization-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:45:47.972Z", "updatedAt": "2025-07-30T13:45:47.972Z", "scannedAt": "2025-07-30T13:45:47.972Z", "path": "role/data-visualization-expert/data-visualization-expert.role.md"}}, {"id": "visualization-development", "source": "project", "protocol": "execution", "name": "Visualization Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/data-visualization-expert/execution/visualization-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:47.974Z", "updatedAt": "2025-07-30T13:45:47.974Z", "scannedAt": "2025-07-30T13:45:47.974Z", "path": "role/data-visualization-expert/execution/visualization-development.execution.md"}}, {"id": "data-visualization", "source": "project", "protocol": "thought", "name": "Data Visualization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/data-visualization-expert/thought/data-visualization.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:47.976Z", "updatedAt": "2025-07-30T13:45:47.976Z", "scannedAt": "2025-07-30T13:45:47.976Z", "path": "role/data-visualization-expert/thought/data-visualization.thought.md"}}, {"id": "database-architect", "source": "project", "protocol": "role", "name": "Database Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/database-architect/database-architect.role.md", "metadata": {"createdAt": "2025-07-30T13:45:47.978Z", "updatedAt": "2025-07-30T13:45:47.978Z", "scannedAt": "2025-07-30T13:45:47.978Z", "path": "role/database-architect/database-architect.role.md"}}, {"id": "database-development", "source": "project", "protocol": "execution", "name": "Database Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/database-architect/execution/database-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:47.979Z", "updatedAt": "2025-07-30T13:45:47.979Z", "scannedAt": "2025-07-30T13:45:47.979Z", "path": "role/database-architect/execution/database-development.execution.md"}}, {"id": "database-design", "source": "project", "protocol": "thought", "name": "Database Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/database-architect/thought/database-design.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:47.980Z", "updatedAt": "2025-07-30T13:45:47.980Z", "scannedAt": "2025-07-30T13:45:47.980Z", "path": "role/database-architect/thought/database-design.thought.md"}}, {"id": "<PERSON><PERSON><PERSON>-engineer", "source": "project", "protocol": "role", "name": "Devops Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/devops-engineer/devops-engineer.role.md", "metadata": {"createdAt": "2025-07-30T13:45:47.982Z", "updatedAt": "2025-07-30T13:45:47.982Z", "scannedAt": "2025-07-30T13:45:47.982Z", "path": "role/devops-engineer/devops-engineer.role.md"}}, {"id": "devops-development", "source": "project", "protocol": "execution", "name": "Devops Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/devops-engineer/execution/devops-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:47.984Z", "updatedAt": "2025-07-30T13:45:47.984Z", "scannedAt": "2025-07-30T13:45:47.984Z", "path": "role/devops-engineer/execution/devops-development.execution.md"}}, {"id": "devops-automation", "source": "project", "protocol": "thought", "name": "Devops Automation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/devops-engineer/thought/devops-automation.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:47.986Z", "updatedAt": "2025-07-30T13:45:47.986Z", "scannedAt": "2025-07-30T13:45:47.986Z", "path": "role/devops-engineer/thought/devops-automation.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-07-30T13:45:47.988Z", "updatedAt": "2025-07-30T13:45:47.988Z", "scannedAt": "2025-07-30T13:45:47.988Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:47.993Z", "updatedAt": "2025-07-30T13:45:47.993Z", "scannedAt": "2025-07-30T13:45:47.993Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:47.995Z", "updatedAt": "2025-07-30T13:45:47.995Z", "scannedAt": "2025-07-30T13:45:47.995Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "electron-developer", "source": "project", "protocol": "role", "name": "Electron Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/electron-developer/electron-developer.role.md", "metadata": {"createdAt": "2025-07-30T13:45:47.997Z", "updatedAt": "2025-07-30T13:45:47.997Z", "scannedAt": "2025-07-30T13:45:47.997Z", "path": "role/electron-developer/electron-developer.role.md"}}, {"id": "electron-development", "source": "project", "protocol": "execution", "name": "Electron Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/electron-developer/execution/electron-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.003Z", "updatedAt": "2025-07-30T13:45:48.003Z", "scannedAt": "2025-07-30T13:45:48.002Z", "path": "role/electron-developer/execution/electron-development.execution.md"}}, {"id": "electron-architecture", "source": "project", "protocol": "thought", "name": "Electron Architecture 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/electron-developer/thought/electron-architecture.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.005Z", "updatedAt": "2025-07-30T13:45:48.005Z", "scannedAt": "2025-07-30T13:45:48.005Z", "path": "role/electron-developer/thought/electron-architecture.thought.md"}}, {"id": "glassmorphism-design-process", "source": "project", "protocol": "execution", "name": "Glassmorphism Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.013Z", "updatedAt": "2025-07-30T13:45:48.013Z", "scannedAt": "2025-07-30T13:45:48.013Z", "path": "role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md"}}, {"id": "glassmorphism-designer", "source": "project", "protocol": "role", "name": "Glassmorphism Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/glassmorphism-designer/glassmorphism-designer.role.md", "metadata": {"createdAt": "2025-07-30T13:45:48.014Z", "updatedAt": "2025-07-30T13:45:48.014Z", "scannedAt": "2025-07-30T13:45:48.014Z", "path": "role/glassmorphism-designer/glassmorphism-designer.role.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.016Z", "updatedAt": "2025-07-30T13:45:48.016Z", "scannedAt": "2025-07-30T13:45:48.016Z", "path": "role/glassmorphism-designer/thought/design-thinking.thought.md"}}, {"id": "nlp-development", "source": "project", "protocol": "execution", "name": "Nlp Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/nlp-expert/execution/nlp-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.021Z", "updatedAt": "2025-07-30T13:45:48.021Z", "scannedAt": "2025-07-30T13:45:48.021Z", "path": "role/nlp-expert/execution/nlp-development.execution.md"}}, {"id": "nlp-expert", "source": "project", "protocol": "role", "name": "Nlp Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/nlp-expert/nlp-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:45:48.022Z", "updatedAt": "2025-07-30T13:45:48.022Z", "scannedAt": "2025-07-30T13:45:48.022Z", "path": "role/nlp-expert/nlp-expert.role.md"}}, {"id": "nlp-processing", "source": "project", "protocol": "thought", "name": "Nlp Processing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/nlp-expert/thought/nlp-processing.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.025Z", "updatedAt": "2025-07-30T13:45:48.025Z", "scannedAt": "2025-07-30T13:45:48.025Z", "path": "role/nlp-expert/thought/nlp-processing.thought.md"}}, {"id": "performance-development", "source": "project", "protocol": "execution", "name": "Performance Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/performance-expert/execution/performance-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.028Z", "updatedAt": "2025-07-30T13:45:48.028Z", "scannedAt": "2025-07-30T13:45:48.028Z", "path": "role/performance-expert/execution/performance-development.execution.md"}}, {"id": "performance-expert", "source": "project", "protocol": "role", "name": "Performance Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/performance-expert/performance-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:45:48.029Z", "updatedAt": "2025-07-30T13:45:48.029Z", "scannedAt": "2025-07-30T13:45:48.029Z", "path": "role/performance-expert/performance-expert.role.md"}}, {"id": "performance-optimization", "source": "project", "protocol": "thought", "name": "Performance Optimization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/performance-expert/thought/performance-optimization.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.030Z", "updatedAt": "2025-07-30T13:45:48.030Z", "scannedAt": "2025-07-30T13:45:48.030Z", "path": "role/performance-expert/thought/performance-optimization.thought.md"}}, {"id": "editor-development", "source": "project", "protocol": "execution", "name": "Editor Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/rich-text-editor-expert/execution/editor-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.032Z", "updatedAt": "2025-07-30T13:45:48.032Z", "scannedAt": "2025-07-30T13:45:48.032Z", "path": "role/rich-text-editor-expert/execution/editor-development.execution.md"}}, {"id": "rich-text-editor-expert", "source": "project", "protocol": "role", "name": "Rich Text Editor Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/rich-text-editor-expert/rich-text-editor-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:45:48.035Z", "updatedAt": "2025-07-30T13:45:48.035Z", "scannedAt": "2025-07-30T13:45:48.035Z", "path": "role/rich-text-editor-expert/rich-text-editor-expert.role.md"}}, {"id": "rich-text-editing", "source": "project", "protocol": "thought", "name": "Rich Text Editing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/rich-text-editor-expert/thought/rich-text-editing.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.037Z", "updatedAt": "2025-07-30T13:45:48.037Z", "scannedAt": "2025-07-30T13:45:48.037Z", "path": "role/rich-text-editor-expert/thought/rich-text-editing.thought.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "execution", "name": "Intelligent Routing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/intelligent-routing.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.039Z", "updatedAt": "2025-07-30T13:45:48.039Z", "scannedAt": "2025-07-30T13:45:48.039Z", "path": "role/system-director/execution/intelligent-routing.execution.md"}}, {"id": "project-management", "source": "project", "protocol": "execution", "name": "Project Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-management.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.041Z", "updatedAt": "2025-07-30T13:45:48.041Z", "scannedAt": "2025-07-30T13:45:48.041Z", "path": "role/system-director/execution/project-management.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.042Z", "updatedAt": "2025-07-30T13:45:48.042Z", "scannedAt": "2025-07-30T13:45:48.042Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "team-coordination", "source": "project", "protocol": "execution", "name": "Team Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/team-coordination.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.043Z", "updatedAt": "2025-07-30T13:45:48.043Z", "scannedAt": "2025-07-30T13:45:48.043Z", "path": "role/system-director/execution/team-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-07-30T13:45:48.043Z", "updatedAt": "2025-07-30T13:45:48.043Z", "scannedAt": "2025-07-30T13:45:48.043Z", "path": "role/system-director/system-director.role.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "thought", "name": "Intelligent Routing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/intelligent-routing.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.045Z", "updatedAt": "2025-07-30T13:45:48.045Z", "scannedAt": "2025-07-30T13:45:48.045Z", "path": "role/system-director/thought/intelligent-routing.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.045Z", "updatedAt": "2025-07-30T13:45:48.045Z", "scannedAt": "2025-07-30T13:45:48.045Z", "path": "role/system-director/thought/quality-control.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.046Z", "updatedAt": "2025-07-30T13:45:48.046Z", "scannedAt": "2025-07-30T13:45:48.046Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "team-coordination", "source": "project", "protocol": "thought", "name": "Team Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/team-coordination.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.047Z", "updatedAt": "2025-07-30T13:45:48.047Z", "scannedAt": "2025-07-30T13:45:48.047Z", "path": "role/system-director/thought/team-coordination.thought.md"}}, {"id": "testing-development", "source": "project", "protocol": "execution", "name": "Testing Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/test-engineer/execution/testing-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:45:48.050Z", "updatedAt": "2025-07-30T13:45:48.050Z", "scannedAt": "2025-07-30T13:45:48.050Z", "path": "role/test-engineer/execution/testing-development.execution.md"}}, {"id": "test-engineer", "source": "project", "protocol": "role", "name": "Test Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/test-engineer/test-engineer.role.md", "metadata": {"createdAt": "2025-07-30T13:45:48.051Z", "updatedAt": "2025-07-30T13:45:48.051Z", "scannedAt": "2025-07-30T13:45:48.051Z", "path": "role/test-engineer/test-engineer.role.md"}}, {"id": "testing-strategy", "source": "project", "protocol": "thought", "name": "Testing Strategy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/test-engineer/thought/testing-strategy.thought.md", "metadata": {"createdAt": "2025-07-30T13:45:48.053Z", "updatedAt": "2025-07-30T13:45:48.053Z", "scannedAt": "2025-07-30T13:45:48.053Z", "path": "role/test-engineer/thought/testing-strategy.thought.md"}}], "stats": {"totalResources": 48, "byProtocol": {"role": 14, "execution": 17, "thought": 17}, "bySource": {"project": 48}}}