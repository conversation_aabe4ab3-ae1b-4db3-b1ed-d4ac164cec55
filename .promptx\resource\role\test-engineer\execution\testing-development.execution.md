<execution>
  <constraint>
    ## 测试约束
    - **测试覆盖率**：代码覆盖率 > 80%，关键功能 > 95%
    - **测试执行时间**：单元测试 < 5分钟，集成测试 < 15分钟
    - **测试稳定性**：自动化测试成功率 > 95%
    - **缺陷发现率**：测试阶段发现缺陷 > 90%
    - **回归测试**：每次发布前必须执行完整回归测试
    - **平台覆盖**：Windows、macOS、Linux三大平台全覆盖
  </constraint>

  <rule>
    ## 测试规则
    - **测试先行**：新功能开发前必须先写测试用例
    - **自动化优先**：可重复的测试必须自动化
    - **数据隔离**：测试间必须保证数据独立性
    - **环境一致**：测试环境必须与生产环境一致
    - **缺陷跟踪**：所有缺陷必须有完整的跟踪记录
    - **测试文档**：测试用例和结果必须有详细文档
  </rule>

  <guideline>
    ## 测试指导原则
    - **质量优先**：测试质量比测试数量更重要
    - **用户视角**：从用户角度设计测试场景
    - **边界测试**：重点测试边界条件和异常情况
    - **持续改进**：根据缺陷分析持续改进测试策略
    - **团队协作**：测试与开发紧密协作
    - **工具辅助**：充分利用自动化测试工具
  </guideline>

  <process>
    ## 测试实施流程
    
    ### Step 1: 单元测试框架
    ```typescript
    // tests/unit/setup.ts
    import { vi } from 'vitest'
    import { config } from '@vue/test-utils'
    
    // 全局测试配置
    config.global.mocks = {
      $t: (key: string) => key, // 国际化mock
      $router: {
        push: vi.fn(),
        replace: vi.fn(),
        go: vi.fn(),
        back: vi.fn(),
        forward: vi.fn()
      },
      $route: {
        path: '/',
        params: {},
        query: {},
        hash: '',
        fullPath: '/',
        matched: [],
        name: undefined,
        redirectedFrom: undefined
      }
    }
    
    // Mock Electron API
    Object.defineProperty(window, 'electronAPI', {
      value: {
        getVersion: vi.fn().mockResolvedValue('1.0.0'),
        openFile: vi.fn().mockResolvedValue('test-file-path'),
        saveFile: vi.fn().mockResolvedValue(undefined),
        dbQuery: vi.fn().mockResolvedValue([]),
        aiGenerate: vi.fn().mockResolvedValue('Generated content')
      },
      writable: true
    })
    
    // 清理函数
    afterEach(() => {
      vi.clearAllMocks()
    })
    ```
    
    ```typescript
    // tests/unit/components/NovelEditor.test.ts
    import { describe, it, expect, vi, beforeEach } from 'vitest'
    import { mount } from '@vue/test-utils'
    import { createPinia, setActivePinia } from 'pinia'
    import NovelEditor from '@/components/editor/NovelEditor.vue'
    import { useEditorStore } from '@/stores/editor'
    
    describe('NovelEditor', () => {
      let wrapper: any
      let editorStore: any
      
      beforeEach(() => {
        setActivePinia(createPinia())
        editorStore = useEditorStore()
        
        wrapper = mount(NovelEditor, {
          props: {
            content: '<p>Test content</p>',
            chapterId: 'test-chapter-1'
          }
        })
      })
      
      it('应该正确渲染编辑器', () => {
        expect(wrapper.find('.novel-editor').exists()).toBe(true)
        expect(wrapper.find('.editor-content').exists()).toBe(true)
      })
      
      it('应该显示正确的字数统计', async () => {
        await wrapper.vm.$nextTick()
        const wordCount = wrapper.find('.word-count')
        expect(wordCount.text()).toContain('字数:')
      })
      
      it('应该处理内容变更', async () => {
        const editorContent = wrapper.find('.editor-content')
        
        // 模拟输入事件
        await editorContent.trigger('input')
        
        expect(wrapper.emitted('update:content')).toBeTruthy()
      })
      
      it('应该支持撤销重做操作', async () => {
        const undoButton = wrapper.find('button[data-testid="undo"]')
        const redoButton = wrapper.find('button[data-testid="redo"]')
        
        // 初始状态应该禁用撤销重做
        expect(undoButton.attributes('disabled')).toBeDefined()
        expect(redoButton.attributes('disabled')).toBeDefined()
        
        // 添加一些内容后应该可以撤销
        editorStore.saveVersion('New content')
        await wrapper.vm.$nextTick()
        
        expect(undoButton.attributes('disabled')).toBeUndefined()
      })
      
      it('应该自动保存内容', async () => {
        const saveContentSpy = vi.spyOn(wrapper.vm, 'saveContent')
        
        // 模拟内容变更
        wrapper.vm.onInput({ target: { innerHTML: 'New content' } })
        
        // 等待防抖延迟
        await new Promise(resolve => setTimeout(resolve, 2100))
        
        expect(saveContentSpy).toHaveBeenCalled()
      })
    })
    ```
    
    ### Step 2: 集成测试
    ```typescript
    // tests/integration/database.test.ts
    import { describe, it, expect, beforeEach, afterEach } from 'vitest'
    import { ProjectService } from '@/database/services/ProjectService'
    import { ChapterService } from '@/database/services/ChapterService'
    import { db } from '@/database/client'
    
    describe('数据库集成测试', () => {
      let projectService: ProjectService
      let chapterService: ChapterService
      let testProjectId: number
      
      beforeEach(async () => {
        projectService = new ProjectService()
        chapterService = new ChapterService()
        
        // 创建测试项目
        const project = await projectService.createProject({
          title: '测试小说',
          genre: '玄幻',
          theme: '修仙',
          style: '第三人称'
        })
        testProjectId = project.id
      })
      
      afterEach(async () => {
        // 清理测试数据
        await projectService.deleteProject(testProjectId)
      })
      
      it('应该能够创建和查询项目', async () => {
        const project = await projectService.getProjectById(testProjectId)
        
        expect(project).toBeTruthy()
        expect(project!.title).toBe('测试小说')
        expect(project!.genre).toBe('玄幻')
      })
      
      it('应该能够创建和管理章节', async () => {
        // 创建章节
        const chapter = await chapterService.createChapter({
          projectId: testProjectId,
          chapterNumber: 1,
          title: '第一章',
          content: '这是第一章的内容'
        })
        
        expect(chapter.projectId).toBe(testProjectId)
        expect(chapter.chapterNumber).toBe(1)
        expect(chapter.title).toBe('第一章')
        
        // 查询章节
        const chapters = await chapterService.getChaptersByProject(testProjectId)
        expect(chapters).toHaveLength(1)
        expect(chapters[0].id).toBe(chapter.id)
      })
      
      it('应该能够更新章节内容', async () => {
        const chapter = await chapterService.createChapter({
          projectId: testProjectId,
          chapterNumber: 1,
          title: '第一章',
          content: '原始内容'
        })
        
        // 更新内容
        const updatedChapter = await chapterService.updateChapter(chapter.id, {
          content: '更新后的内容',
          wordCount: 7
        })
        
        expect(updatedChapter.content).toBe('更新后的内容')
        expect(updatedChapter.wordCount).toBe(7)
      })
      
      it('应该正确处理事务', async () => {
        await expect(async () => {
          await db.$transaction(async (tx) => {
            // 创建章节
            await tx.chapter.create({
              data: {
                projectId: testProjectId,
                chapterNumber: 1,
                title: '测试章节'
              }
            })
            
            // 故意抛出错误
            throw new Error('测试事务回滚')
          })
        }).rejects.toThrow('测试事务回滚')
        
        // 验证事务已回滚
        const chapters = await chapterService.getChaptersByProject(testProjectId)
        expect(chapters).toHaveLength(0)
      })
    })
    ```
    
    ### Step 3: E2E测试
    ```typescript
    // tests/e2e/novel-creation.spec.ts
    import { test, expect } from '@playwright/test'
    import { ElectronApplication, Page, _electron as electron } from 'playwright'
    
    let electronApp: ElectronApplication
    let page: Page
    
    test.beforeAll(async () => {
      // 启动Electron应用
      electronApp = await electron.launch({
        args: ['dist-main/index.js'],
        env: {
          NODE_ENV: 'test'
        }
      })
      
      // 获取第一个窗口
      page = await electronApp.firstWindow()
      
      // 等待应用加载完成
      await page.waitForLoadState('domcontentloaded')
    })
    
    test.afterAll(async () => {
      await electronApp.close()
    })
    
    test.describe('小说创作流程', () => {
      test('应该能够创建新项目', async () => {
        // 点击创建新项目按钮
        await page.click('[data-testid="create-project"]')
        
        // 填写项目信息
        await page.fill('[data-testid="project-title"]', '测试小说')
        await page.selectOption('[data-testid="project-genre"]', '玄幻')
        await page.fill('[data-testid="project-theme"]', '修仙成神')
        
        // 提交表单
        await page.click('[data-testid="submit-project"]')
        
        // 验证项目创建成功
        await expect(page.locator('[data-testid="project-title-display"]')).toHaveText('测试小说')
      })
      
      test('应该能够创建和编辑章节', async () => {
        // 创建新章节
        await page.click('[data-testid="create-chapter"]')
        
        // 填写章节标题
        await page.fill('[data-testid="chapter-title"]', '第一章 初入修仙界')
        
        // 在编辑器中输入内容
        const editor = page.locator('[data-testid="chapter-editor"]')
        await editor.click()
        await editor.fill('这是第一章的内容，主角开始了修仙之路...')
        
        // 保存章节
        await page.keyboard.press('Control+S')
        
        // 验证保存状态
        await expect(page.locator('[data-testid="save-status"]')).toHaveText('已保存')
        
        // 验证字数统计
        const wordCount = page.locator('[data-testid="word-count"]')
        await expect(wordCount).toContainText('字数:')
      })
      
      test('应该能够使用AI生成功能', async () => {
        // 打开AI生成对话框
        await page.click('[data-testid="ai-generate"]')
        
        // 输入生成提示
        await page.fill('[data-testid="ai-prompt"]', '生成一个修仙小说的开头')
        
        // 点击生成按钮
        await page.click('[data-testid="generate-button"]')
        
        // 等待生成完成
        await page.waitForSelector('[data-testid="generated-content"]', { timeout: 10000 })
        
        // 验证生成的内容
        const generatedContent = page.locator('[data-testid="generated-content"]')
        await expect(generatedContent).not.toBeEmpty()
        
        // 插入生成的内容
        await page.click('[data-testid="insert-content"]')
        
        // 验证内容已插入编辑器
        const editor = page.locator('[data-testid="chapter-editor"]')
        await expect(editor).not.toBeEmpty()
      })
      
      test('应该能够查看统计信息', async () => {
        // 打开统计页面
        await page.click('[data-testid="statistics"]')
        
        // 验证统计图表显示
        await expect(page.locator('[data-testid="word-count-chart"]')).toBeVisible()
        await expect(page.locator('[data-testid="progress-chart"]')).toBeVisible()
        
        // 验证统计数据
        const totalWords = page.locator('[data-testid="total-words"]')
        await expect(totalWords).toContainText('总字数:')
        
        const chapterCount = page.locator('[data-testid="chapter-count"]')
        await expect(chapterCount).toContainText('章节数:')
      })
    })
    ```
    
    ### Step 4: 性能测试
    ```typescript
    // tests/performance/editor-performance.test.ts
    import { test, expect } from '@playwright/test'
    import { ElectronApplication, _electron as electron } from 'playwright'
    
    test.describe('编辑器性能测试', () => {
      let electronApp: ElectronApplication
      
      test.beforeAll(async () => {
        electronApp = await electron.launch({
          args: ['dist-main/index.js']
        })
      })
      
      test.afterAll(async () => {
        await electronApp.close()
      })
      
      test('大文档编辑性能测试', async () => {
        const page = await electronApp.firstWindow()
        
        // 创建大文档内容
        const largeContent = '这是一个很长的段落。'.repeat(10000)
        
        // 测量加载时间
        const startTime = Date.now()
        
        const editor = page.locator('[data-testid="chapter-editor"]')
        await editor.fill(largeContent)
        
        const loadTime = Date.now() - startTime
        
        // 验证加载时间在合理范围内
        expect(loadTime).toBeLessThan(2000) // 2秒内
        
        // 测量输入响应时间
        const inputStartTime = Date.now()
        await editor.type('新增内容')
        const inputTime = Date.now() - inputStartTime
        
        expect(inputTime).toBeLessThan(100) // 100ms内
      })
      
      test('内存使用测试', async () => {
        const page = await electronApp.firstWindow()
        
        // 获取初始内存使用
        const initialMemory = await page.evaluate(() => {
          return (performance as any).memory?.usedJSHeapSize || 0
        })
        
        // 执行一系列操作
        for (let i = 0; i < 100; i++) {
          await page.click('[data-testid="create-chapter"]')
          await page.fill('[data-testid="chapter-title"]', `第${i}章`)
          await page.keyboard.press('Escape') // 取消创建
        }
        
        // 获取操作后内存使用
        const finalMemory = await page.evaluate(() => {
          return (performance as any).memory?.usedJSHeapSize || 0
        })
        
        // 验证内存增长在合理范围内
        const memoryIncrease = finalMemory - initialMemory
        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024) // 50MB内
      })
    })
    ```
    
    ### Step 5: 测试配置
    ```typescript
    // vitest.config.ts
    import { defineConfig } from 'vitest/config'
    import vue from '@vitejs/plugin-vue'
    import { resolve } from 'path'
    
    export default defineConfig({
      plugins: [vue()],
      test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: ['tests/unit/setup.ts'],
        coverage: {
          provider: 'v8',
          reporter: ['text', 'json', 'html'],
          exclude: [
            'node_modules/',
            'tests/',
            'dist/',
            '**/*.d.ts'
          ],
          thresholds: {
            global: {
              branches: 80,
              functions: 80,
              lines: 80,
              statements: 80
            }
          }
        }
      },
      resolve: {
        alias: {
          '@': resolve(__dirname, 'src')
        }
      }
    })
    ```
    
    ```typescript
    // playwright.config.ts
    import { defineConfig, devices } from '@playwright/test'
    
    export default defineConfig({
      testDir: './tests/e2e',
      fullyParallel: true,
      forbidOnly: !!process.env.CI,
      retries: process.env.CI ? 2 : 0,
      workers: process.env.CI ? 1 : undefined,
      reporter: 'html',
      use: {
        trace: 'on-first-retry',
        screenshot: 'only-on-failure'
      },
      projects: [
        {
          name: 'electron',
          use: { ...devices['Desktop Chrome'] }
        }
      ]
    })
    ```
  </process>

  <criteria>
    ## 质量标准
    
    ### 测试覆盖率
    - ✅ 代码覆盖率 > 80%
    - ✅ 关键功能覆盖率 > 95%
    - ✅ 分支覆盖率 > 75%
    - ✅ 函数覆盖率 > 85%
    
    ### 测试质量
    - ✅ 测试用例设计合理
    - ✅ 边界条件测试完整
    - ✅ 异常情况处理验证
    - ✅ 性能指标验证
    
    ### 自动化程度
    - ✅ 单元测试100%自动化
    - ✅ 集成测试90%自动化
    - ✅ E2E测试80%自动化
    - ✅ 回归测试完全自动化
    
    ### 测试效率
    - ✅ 测试执行时间合理
    - ✅ 测试维护成本可控
    - ✅ 缺陷发现及时
    - ✅ 测试结果可靠
  </criteria>
</execution>
